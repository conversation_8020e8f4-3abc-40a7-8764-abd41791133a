# Glasses 3D Orientation Fix - Camera Facing

## Overview
This document explains the fixes applied to ensure the 3D glasses model faces the camera (outward) properly in the virtual try-on application.

## Problem
The 3D glasses model was oriented to face the eyes (inward) instead of facing the camera (outward), making it appear backwards to the user.

## Solution Applied

### 1. Base Model Orientation Fix
**File**: `src/lib/three/glasses-renderer.ts`

Added automatic forward-facing orientation when loading the 3D model:
```typescript
// Set initial orientation to face forward
// Most 3D glasses models need to be rotated to face the camera properly
this.glassesModel.rotation.set(0, Math.PI, 0) // Rotate 180° around Y-axis to face forward
```

### 2. Rotation Calculation Update
**File**: `src/lib/three/glasses-renderer.ts`

Updated the rotation method to maintain forward-facing while following face movements:
```typescript
updateGlassesRotation(rotation: { x: number; y: number; z: number }) {
  // Apply face rotation while maintaining forward-facing orientation
  // The base rotation (0, Math.PI, 0) ensures glasses face forward
  // Then we add the face rotation on top of that
  this.glassesModel.rotation.set(
    rotation.x,                    // Pitch (up/down head movement)
    Math.PI + rotation.y,          // Yaw (left/right head movement) + base forward rotation
    rotation.z                     // Roll (head tilt)
  )
}
```

### 3. Face Rotation Sensitivity Adjustment
**File**: `src/lib/mediapipe/face-detection.ts`

Improved face rotation calculation for more stable glasses positioning:
```typescript
// Reduce yaw sensitivity for more stable glasses positioning
const yaw = Math.atan2(noseOffset, eyeDistance) * 1.5

// Adjust pitch to keep glasses naturally positioned
const pitch = (Math.atan2(noseHeight, faceHeight) - 0.3) * 0.8
```

### 4. Debug Functions Added
**File**: `src/components/try-on/try-on-canvas.tsx`

Added global debug functions for testing orientation:
```javascript
// Test different orientations
window.debugGlasses.testOrientation()

// Set custom orientation (in degrees or radians)
window.debugGlasses.setOrientation(0, 180, 0)

// Reset to forward-facing
window.debugGlasses.resetToForward()
```

## Testing the Fix

### 1. Basic Test
Open the browser console and run:
```javascript
window.debugGlasses.testOrientation()
```
This will cycle through different orientations to help identify the correct one.

### 2. Manual Orientation Test
If the automatic forward-facing doesn't work for your specific 3D model:
```javascript
// Try different Y rotations (in degrees)
window.debugGlasses.setOrientation(0, 0, 0)    // Default
window.debugGlasses.setOrientation(0, 90, 0)   // 90° turn
window.debugGlasses.setOrientation(0, 180, 0)  // 180° turn (current default)
window.debugGlasses.setOrientation(0, 270, 0)  // 270° turn
```

### 3. Reset to Forward
```javascript
window.debugGlasses.resetToForward()
```

## Expected Results

After applying these fixes:

1. **Forward-Facing**: Glasses should face the camera/user by default
2. **Natural Movement**: Glasses should follow face movements naturally
3. **Stable Tracking**: Reduced jitter and more stable positioning
4. **Proper Alignment**: Glasses should align with the blue dots (face landmarks)

## Troubleshooting

### If glasses still appear backwards:
1. Try different base orientations using the debug functions
2. Check if your 3D model has a different default orientation
3. Adjust the base rotation in `loadGlassesModel()` method

### If glasses are sideways:
1. Adjust the X or Z rotation in the base orientation
2. Use `window.debugGlasses.setOrientation(90, 180, 0)` to test

### If tracking is unstable:
1. The sensitivity values in `calculateFaceRotation()` can be further adjusted
2. Reduce the multipliers (currently 1.5 for yaw, 0.8 for pitch)

## Technical Details

### Coordinate System
- **X-axis**: Pitch (up/down head movement)
- **Y-axis**: Yaw (left/right head movement) 
- **Z-axis**: Roll (head tilt)

### Base Rotation
- `(0, Math.PI, 0)` = 180° rotation around Y-axis
- This makes the glasses face forward in most standard 3D models

### Face Rotation Integration
- Face rotation is added to the base rotation
- This allows natural head movement tracking while maintaining forward orientation

## Future Improvements

1. **Auto-Detection**: Automatically detect the correct orientation for different 3D models
2. **Model Metadata**: Store orientation preferences per glasses model
3. **User Calibration**: Allow users to manually calibrate orientation per model
4. **Advanced Tracking**: Implement more sophisticated face orientation tracking

## Conclusion

The glasses should now face forward properly while maintaining natural head movement tracking. Use the debug functions to fine-tune orientation for specific 3D models if needed.
