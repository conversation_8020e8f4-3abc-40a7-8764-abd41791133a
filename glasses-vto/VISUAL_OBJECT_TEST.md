# Visual Object Test - Troubleshooting Guide

## Current Issues Identified

### 1. ❌ Model Loading Errors
```
GET /models/aviator-glasses.gltf 404 in 607ms
```

**Root Cause**: System trying to load non-existent model files

### 2. ❌ Available Models Status
- ✅ `/models/default-glasses.gltf` - EXISTS
- ✅ `/models/simple-glasses.gltf` - EXISTS  
- ❌ `/models/aviator-glasses.gltf` - NOT FOUND
- ✅ `/models/simple-test-cube.gltf` - EXISTS (newly created)

## Visual Test Checklist

### Step 1: Basic Model Loading Test
1. **Open Debug Page**: `http://localhost:3001/debug`
2. **Click "Test Model Load"** for each model
3. **Expected Results**:
   - ✅ Default Glasses: "Available"
   - ✅ Simple Glasses: "Available"  
   - ✅ Test Cube: "Available"

### Step 2: Camera and Object Visibility Test
1. **Click "Start Camera"** - should show video feed
2. **Click "Debug ON"** - should enable debug mode
3. **Look for**:
   - Blue dots on face (MediaPipe landmarks)
   - 3D object overlay (glasses/cube)
   - Debug statistics overlay

### Step 3: Manual Object Positioning Test
1. **Open Browser Console** (F12)
2. **Run Commands**:
   ```javascript
   // Test center positioning
   window.debugGlasses.testCenter()
   
   // Test manual positioning  
   window.debugGlasses.testManual(320, 0, -100, 2)
   
   // Check if object is loaded
   window.debugGlasses.getGlasses()
   ```

### Step 4: Expected Visual Results

#### ✅ SUCCESS Indicators:
- **Video Feed**: Clear camera feed with mirror effect
- **Blue Dots**: Visible face landmarks (eyes, nose, mouth)
- **3D Object**: Visible glasses/cube at appropriate size
- **Debug Overlay**: FPS counter, face detection stats
- **Console Logs**: Model loading success messages

#### ❌ FAILURE Indicators:
- **No Video**: Camera permission or initialization issues
- **No Blue Dots**: MediaPipe face detection failure
- **No 3D Object**: Model loading or rendering failure
- **Console Errors**: 404 errors, WebGL errors, etc.

## Troubleshooting Steps

### If No Object Visible:

#### 1. Check Model Loading
```javascript
// In browser console
console.log('Checking model status...')
window.debugGlasses.getGlasses()
```

**Expected Output**:
```javascript
{
  hasModel: true,
  modelVisible: true,
  position: { x: 320, y: -240, z: -100 },
  scale: { x: 1.0, y: 1.0, z: 1.0 }
}
```

#### 2. Force Object Visibility
```javascript
// Force render test cube
window.debugGlasses.testCube()

// Force center positioning
window.debugGlasses.testCenter()
```

#### 3. Check WebGL Support
```javascript
// Test WebGL
const canvas = document.createElement('canvas')
const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
console.log('WebGL supported:', !!gl)
```

### If Object Too Small/Large:

#### 1. Adjust Scale
```javascript
// Test different scales
window.debugGlasses.testManual(320, 0, -100, 1)   // Small
window.debugGlasses.testManual(320, 0, -100, 5)   // Medium  
window.debugGlasses.testManual(320, 0, -100, 10)  // Large
```

#### 2. Check Camera Distance
```javascript
// Get camera info
window.debugGlasses.getCamera()
```

**Expected**: Camera at z=-300, object at z=-100 to z=-200

### If Object in Wrong Position:

#### 1. Test Positioning
```javascript
// Test different positions
window.debugGlasses.testFace(0.5, 0.5)  // Center
window.debugGlasses.testFace(0.3, 0.4)  // Left eye area
window.debugGlasses.testFace(0.7, 0.4)  // Right eye area
```

#### 2. Check Coordinate System
```javascript
// Manual positioning test
window.debugGlasses.testManual(100, 0, -100, 2)   // Left
window.debugGlasses.testManual(320, 0, -100, 2)   // Center
window.debugGlasses.testManual(540, 0, -100, 2)   // Right
```

## Quick Visual Test Commands

### Immediate Visibility Test:
```javascript
// 1. Load test cube (should be very visible)
window.debugGlasses.testCube()

// 2. Position at screen center
window.debugGlasses.testCenter()

// 3. Check if anything is rendered
window.debugGlasses.getGlasses()
```

### Scale Test Sequence:
```javascript
// Test different scales to find visible size
window.debugGlasses.testManual(320, 0, -100, 0.5)  // Very small
setTimeout(() => window.debugGlasses.testManual(320, 0, -100, 1), 2000)
setTimeout(() => window.debugGlasses.testManual(320, 0, -100, 2), 4000)
setTimeout(() => window.debugGlasses.testManual(320, 0, -100, 5), 6000)
```

### Position Test Sequence:
```javascript
// Test different Z positions (depth)
window.debugGlasses.testManual(320, 0, -50, 2)   // Very close
setTimeout(() => window.debugGlasses.testManual(320, 0, -100, 2), 2000)
setTimeout(() => window.debugGlasses.testManual(320, 0, -150, 2), 4000)
setTimeout(() => window.debugGlasses.testManual(320, 0, -200, 2), 6000)
```

## Expected Debug Console Output

### Successful Model Loading:
```
📦 GLTF loaded successfully: {scene: Group, ...}
👓 Glasses model info: {children: 1, position: Vector3, ...}
✅ Processed 1 meshes, adding to scene
🧪 TEST: Positioned glasses at center for visibility test
```

### Successful Positioning:
```
🎯 COORDINATE CONVERSION (MIRRORED): {
  normalized: {x: 0.500, y: 0.400},
  world: {x: 320.0, y: 48.0},
  mirrorFormula: "worldX = 640 - 320.0 = 320.0"
}
```

### Successful Scale Application:
```
🔧 Scale applied: {
  requestedScale: 5,
  appliedScale: 1.0,
  reduction: "90% reduction to prevent oversizing"
}
```

## Final Verification

### Visual Checklist:
- [ ] Camera feed visible and mirrored
- [ ] Blue face landmarks visible
- [ ] 3D object visible at appropriate size
- [ ] Object follows face movement
- [ ] Debug overlay shows statistics
- [ ] No console errors

### Performance Checklist:
- [ ] FPS > 15 (acceptable)
- [ ] Face detection rate > 80%
- [ ] No memory leaks
- [ ] Smooth object tracking

If all checks pass, the visual try-on system is working correctly! 🎉
