import { TryOnCanvas } from '@/components/try-on/try-on-canvas'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
import { getModelUrl } from '@/lib/api/storage-client'

async function getProductData(productId: string) {
  try {
    // Use public API endpoints instead of admin endpoints
    const { createClient } = await import('@/lib/supabase/server')
    const supabase = await createClient()

    // Get product data
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('*')
      .eq('id', productId)
      .eq('active', true)
      .single()

    if (productError || !product) {
      throw new Error('Product not found')
    }

    // Get variants
    const { data: variants, error: variantsError } = await supabase
      .from('product_variants')
      .select('*')
      .eq('product_id', productId)
      .eq('active', true)

    return {
      product,
      variants: variants || []
    }
  } catch (error) {
    console.error('Error fetching product:', error)
    return { product: null, variants: [] }
  }
}

export default async function TryOnPage({
  params
}: {
  params: Promise<{ productId: string }>
}) {
  const { productId } = await params
  const { product, variants } = await getProductData(productId)

  // Get the first variant with a GLB model, or use default
  const defaultVariant = variants.find((v: any) => v.glb_path) || variants[0]
  const modelUrl = defaultVariant?.glb_path
    ? getModelUrl(defaultVariant.glb_path)
    : '/models/default-glasses.glb'

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/catalog">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Catalog
          </Link>
        </Button>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        <div>
          <h1 className="text-2xl font-bold mb-4">
            Virtual Try-On{product ? ` - ${product.name}` : ''}
          </h1>
          <TryOnCanvas
            productId={productId}
            variantId={defaultVariant?.id || "default"}
            modelUrl={modelUrl}
          />
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-3">How to Use</h2>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
              <li>Click "Start Camera" to enable your webcam</li>
              <li>Position your face in the center of the frame</li>
              <li>The glasses will automatically appear on your face</li>
              <li>Adjust brightness and size using the sliders</li>
              <li>Take a snapshot to save your look</li>
            </ol>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-3">Tips for Best Results</h2>
            <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
              <li>Ensure good lighting on your face</li>
              <li>Face the camera directly for accurate positioning</li>
              <li>Remove any existing glasses for better tracking</li>
              <li>Keep your face within the frame</li>
            </ul>
          </div>

          <div className="p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Privacy Note:</strong> All processing happens on your device. 
              Your camera feed is never sent to our servers.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}