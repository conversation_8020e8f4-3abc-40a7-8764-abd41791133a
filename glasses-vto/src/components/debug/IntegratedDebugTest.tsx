'use client'

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { FaceDetection } from '@/lib/mediapipe/face-detection'
import { GlassesRenderer } from '@/lib/three/glasses-renderer'

export function IntegratedDebugTest() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null)
  const faceDetectorRef = useRef<FaceDetection | null>(null)
  const glassesRendererRef = useRef<GlassesRenderer | null>(null)
  const animationFrameRef = useRef<number | null>(null)

  const [status, setStatus] = useState<string>('Not started')
  const [isCameraOn, setIsCameraOn] = useState(false)
  const [isDetecting, setIsDetecting] = useState(false)
  const [glassesLoaded, setGlassesLoaded] = useState(false)
  const [position, setPosition] = useState({ x: 320, y: 0, z: -150 })
  const [scale, setScale] = useState(30)

  // Initialize MediaPipe and Three.js
  const initializeComponents = async () => {
    try {
      setStatus('Loading components...')
      
      // Initialize Face Detector
      if (!faceDetectorRef.current) {
        faceDetectorRef.current = new FaceDetection()
        await faceDetectorRef.current.initialize()
        console.log('✅ Face detector initialized')
      }

      // Initialize Glasses Renderer
      if (overlayCanvasRef.current && !glassesRendererRef.current) {
        console.log('🎨 Initializing GlassesRenderer...')

        // Get actual video dimensions if available
        const videoWidth = videoRef.current?.videoWidth || 640
        const videoHeight = videoRef.current?.videoHeight || 480

        console.log('📐 Canvas dimensions:', { videoWidth, videoHeight })

        // Set canvas size to match video
        overlayCanvasRef.current.width = videoWidth
        overlayCanvasRef.current.height = videoHeight

        glassesRendererRef.current = new GlassesRenderer(
          overlayCanvasRef.current,
          videoWidth,
          videoHeight
        )

        // Load simple test model
        try {
          console.log('🔄 Loading glasses model...')
          await glassesRendererRef.current.loadGlassesModel('/models/simple-glasses.gltf', 'debug-test')
          setGlassesLoaded(true)
          console.log('✅ Glasses model loaded successfully')

          // Test render immediately after loading
          console.log('🧪 Testing initial render...')
          glassesRendererRef.current.render()

        } catch (modelError) {
          console.error('❌ Model loading failed:', modelError)
          console.log('🔄 Trying fallback model...')

          // Try default model as fallback
          try {
            await glassesRendererRef.current.loadGlassesModel('/models/default-glasses.gltf', 'debug-test')
            setGlassesLoaded(true)
            console.log('✅ Fallback model loaded')
            glassesRendererRef.current.render()
          } catch (fallbackError) {
            console.error('❌ Fallback model also failed:', fallbackError)
          }
        }
      }

      setStatus('Components loaded successfully!')
      
    } catch (error) {
      console.error('Initialization error:', error)
      setStatus(`Error: ${error.message}`)
    }
  }

  // Start camera
  const startCamera = async () => {
    try {
      setStatus('Starting camera...')
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
        
        // Wait for video to be ready
        await new Promise<void>((resolve) => {
          const checkVideoReady = () => {
            if (videoRef.current && videoRef.current.videoWidth > 0) {
              resolve()
            } else {
              setTimeout(checkVideoReady, 100)
            }
          }
          checkVideoReady()
        })

        // Resize renderer to match video
        if (glassesRendererRef.current && videoRef.current.videoWidth > 0) {
          const videoWidth = videoRef.current.videoWidth
          const videoHeight = videoRef.current.videoHeight

          if (overlayCanvasRef.current) {
            // Set canvas internal resolution to match video
            overlayCanvasRef.current.width = videoWidth
            overlayCanvasRef.current.height = videoHeight

            // Keep CSS size for display (will be scaled by CSS)
            console.log('📐 Canvas resized:', {
              internalSize: `${videoWidth}x${videoHeight}`,
              displaySize: `${overlayCanvasRef.current.clientWidth}x${overlayCanvasRef.current.clientHeight}`
            })
          }

          glassesRendererRef.current.resize(videoWidth, videoHeight)
          console.log('🎥 Resized to video dimensions:', { videoWidth, videoHeight })
        }

        setIsCameraOn(true)
        setStatus('Camera started - ready for detection')
        
        // Start detection loop
        startDetection()
      }
      
    } catch (error) {
      console.error('Camera error:', error)
      setStatus(`Camera error: ${error.message}`)
    }
  }

  // Detection loop
  const detectAndRender = useCallback(() => {
    if (!videoRef.current || !faceDetectorRef.current || !glassesRendererRef.current) {
      animationFrameRef.current = requestAnimationFrame(detectAndRender)
      return
    }

    const video = videoRef.current
    if (video.readyState < video.HAVE_ENOUGH_DATA) {
      animationFrameRef.current = requestAnimationFrame(detectAndRender)
      return
    }

    try {
      // Detect faces
      const results = faceDetectorRef.current.detectFaces(video)

      if (results && results.faceLandmarks && results.faceLandmarks.length > 0) {
        const landmarks = results.faceLandmarks
        const keyPoints = faceDetectorRef.current.getFaceKeyPoints(landmarks)

        if (keyPoints) {
          // Draw face landmarks on canvas
          if (canvasRef.current) {
            const canvas = canvasRef.current
            const ctx = canvas.getContext('2d')
            if (ctx) {
              // Clear canvas
              ctx.clearRect(0, 0, canvas.width, canvas.height)
              
              // Draw video frame
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
              
              // Draw landmarks
              drawLandmarks(ctx, landmarks[0], canvas.width, canvas.height)
              
              // Draw key points
              drawKeyPoints(ctx, keyPoints, canvas.width, canvas.height)
            }
          }

          // Update glasses position using new blue dots method
          const rotation = faceDetectorRef.current.calculateFaceRotation(keyPoints)
          const baseScale = faceDetectorRef.current.calculateGlassesScale(keyPoints)

          glassesRendererRef.current.updateFromFaceLandmarksWithBlueDots(
            keyPoints,
            rotation,
            baseScale,
            'center', // anchor to center blue dot
            { x: 0, y: 0, z: 0 },
            true // use auto scale
          )

          setIsDetecting(true)
          setStatus(`Detecting faces: 1 face found`)
        }
      } else {
        setIsDetecting(false)
        setStatus('No face detected')
      }

      // Render glasses with debugging
      try {
        glassesRendererRef.current.render()

        // Debug render every few frames
        if (Math.random() < 0.01) { // 1% chance per frame
          console.log('🎨 Render debug:', {
            canvasSize: `${overlayCanvasRef.current?.width}x${overlayCanvasRef.current?.height}`,
            hasModel: !!glassesRendererRef.current.glassesModel,
            rendererInfo: {
              scene: !!glassesRendererRef.current.scene,
              camera: !!glassesRendererRef.current.camera,
              renderer: !!glassesRendererRef.current.renderer
            }
          })
        }
      } catch (renderError) {
        console.error('❌ Render error:', renderError)
      }

    } catch (error) {
      console.error('Detection error:', error)
    }

    animationFrameRef.current = requestAnimationFrame(detectAndRender)
  }, [])

  // Draw face landmarks
  const drawLandmarks = (ctx: CanvasRenderingContext2D, landmarks: any, width: number, height: number) => {
    ctx.fillStyle = 'red'
    landmarks.forEach((landmark: any) => {
      ctx.beginPath()
      ctx.arc(landmark.x * width, landmark.y * height, 1, 0, 2 * Math.PI)
      ctx.fill()
    })
  }

  // Draw key points
  const drawKeyPoints = (ctx: CanvasRenderingContext2D, keyPoints: any, width: number, height: number) => {
    ctx.fillStyle = 'blue'
    const points = [
      { name: 'midEye', point: keyPoints.midEye },
      { name: 'leftEye', point: keyPoints.leftEye },
      { name: 'rightEye', point: keyPoints.rightEye },
      { name: 'noseBottom', point: keyPoints.noseBottom }
    ]

    points.forEach(({ name, point }) => {
      ctx.beginPath()
      ctx.arc(point.x * width, point.y * height, 4, 0, 2 * Math.PI)
      ctx.fill()
      
      // Label
      ctx.fillStyle = 'white'
      ctx.font = '12px Arial'
      ctx.fillText(name, point.x * width + 6, point.y * height - 6)
      ctx.fillStyle = 'blue'
    })
  }

  const startDetection = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
    animationFrameRef.current = requestAnimationFrame(detectAndRender)
  }

  const updatePosition = (newPos: Partial<typeof position>) => {
    const updated = { ...position, ...newPos }
    setPosition(updated)
    
    if (glassesRendererRef.current) {
      glassesRendererRef.current.manualTestPosition(updated.x, updated.y, updated.z, scale)
    }
  }

  const updateScale = (newScale: number) => {
    setScale(newScale)
    
    if (glassesRendererRef.current) {
      glassesRendererRef.current.manualTestPosition(position.x, position.y, position.z, newScale)
    }
  }

  useEffect(() => {
    initializeComponents()
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      if (faceDetectorRef.current) {
        faceDetectorRef.current.dispose()
      }
      if (glassesRendererRef.current) {
        glassesRendererRef.current.dispose()
      }
    }
  }, [])

  return (
    <Card className="p-6">
      <h2 className="text-2xl font-bold mb-4">Integrated Debug Test</h2>
      
      <div className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={startCamera} disabled={isCameraOn}>
            {isCameraOn ? 'Camera Active' : 'Start Camera'}
          </Button>
          <div className="text-sm text-gray-600 flex items-center">
            Status: {status}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Camera and Face Detection */}
          <div>
            <h3 className="font-semibold mb-2">Face Detection</h3>
            <div className="relative">
              <video
                ref={videoRef}
                className="hidden"
                playsInline
                muted
              />
              <canvas
                ref={canvasRef}
                width={640}
                height={480}
                className="border rounded w-full"
              />
            </div>
            <p className="text-xs text-gray-600 mt-2">
              Blue dots show key landmarks: 168 (midEye), 143 (leftEye), 372 (rightEye), 2 (noseBottom)<br/>
              Red dots show all other face landmarks.
            </p>
          </div>

          {/* 3D Glasses Overlay */}
          <div>
            <h3 className="font-semibold mb-2">3D Glasses Positioning</h3>
            <div className="relative">
              <canvas
                ref={overlayCanvasRef}
                width={1280}
                height={720}
                className="border rounded w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              />

              {/* Test Buttons */}
              <div className="absolute top-2 left-2 flex flex-col gap-1">
                <div className="flex gap-1">
                  <button
                    onClick={() => {
                      if (glassesRendererRef.current) {
                        console.log('🧪 Manual test render at center...')
                        // Use actual video dimensions
                        const videoWidth = videoRef.current?.videoWidth || 1280
                        const videoHeight = videoRef.current?.videoHeight || 720
                        glassesRendererRef.current.testFacePosition(0.5, 0.4, videoWidth, videoHeight)
                      }
                    }}
                    className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                  >
                    Test Center
                  </button>

                  <button
                    onClick={() => {
                      if (glassesRendererRef.current) {
                        console.log('🔍 Debug canvas state...')
                        glassesRendererRef.current.debugCanvasState()
                      }
                    }}
                    className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                  >
                    Debug Canvas
                  </button>
                </div>

                <div className="flex gap-1">
                  <button
                    onClick={() => {
                      if (glassesRendererRef.current) {
                        console.log('🧪 Test simple geometry...')
                        glassesRendererRef.current.testRenderSimpleGeometry()
                      }
                    }}
                    className="px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600"
                  >
                    Test Cube
                  </button>

                  <button
                    onClick={() => {
                      if (glassesRendererRef.current) {
                        console.log('🧪 Render simple glasses...')
                        glassesRendererRef.current.renderSimpleGlassesShape()
                      }
                    }}
                    className="px-2 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600"
                  >
                    Simple Glasses
                  </button>

                  <button
                    onClick={() => {
                      if (glassesRendererRef.current) {
                        console.log('🎨 Toggle debug background...')
                        glassesRendererRef.current.toggleDebugBackground()
                      }
                    }}
                    className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                  >
                    Toggle BG
                  </button>
                </div>
              </div>

              {/* Canvas Debug Info */}
              <div className="absolute bottom-2 right-2 text-xs text-white bg-black bg-opacity-75 px-2 py-1 rounded">
                Canvas: {overlayCanvasRef.current?.width || 0}x{overlayCanvasRef.current?.height || 0}<br/>
                Video: {videoRef.current?.videoWidth || 0}x{videoRef.current?.videoHeight || 0}<br/>
                Renderer: {glassesRendererRef.current ? '✅' : '❌'}
              </div>
            </div>
            <p className="text-xs text-gray-600 mt-2">
              Glasses should align with blue dots from face detection.<br/>
              Status: {glassesLoaded ? '✅ Glasses loaded' : '⏳ Loading glasses...'}
            </p>
          </div>
        </div>

        {/* Manual Controls */}
        <div className="space-y-4">
          <h3 className="font-semibold">Manual Position Controls</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">X Position: {position.x}</label>
              <Slider
                value={[position.x]}
                onValueChange={([value]) => updatePosition({ x: value })}
                min={0}
                max={1280}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Y Position: {position.y}</label>
              <Slider
                value={[position.y]}
                onValueChange={([value]) => updatePosition({ y: value })}
                min={-360}
                max={360}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Z Position: {position.z}</label>
              <Slider
                value={[position.z]}
                onValueChange={([value]) => updatePosition({ z: value })}
                min={-500}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Scale: {scale}</label>
              <Slider
                value={[scale]}
                onValueChange={([value]) => updateScale(value)}
                min={1}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
