{"asset": {"version": "2.0", "generator": "Manual creation for testing"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0}], "meshes": [{"primitives": [{"attributes": {"POSITION": 0}, "indices": 1, "material": 0}]}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.0, 0.0, 1.0], "metallicFactor": 0.0, "roughnessFactor": 1.0}}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 8, "type": "VEC3", "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0]}, {"bufferView": 1, "componentType": 5123, "count": 36, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 96}, {"buffer": 0, "byteOffset": 96, "byteLength": 72}], "buffers": [{"byteLength": 168, "uri": "data:application/octet-stream;base64,AACAPwAAgD8AAIA/AACAPwAAgD8AAIC/AACAPwAAgL8AAIA/AACAPwAAgL8AAIC/AACAvwAAgD8AAIA/AACAvwAAgD8AAIC/AACAvwAAgL8AAIA/AACAvwAAgL8AAIC/AAEAAgADAAIAAQAEAAUABgAHAAYABQAIAAkACgALAAoACQAMAAoADQAOAA0ACgAPABAAEQASABEAEAAUABMAFQAWABUAEwA="}]}