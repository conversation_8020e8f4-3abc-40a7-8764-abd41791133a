import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

// Types for blue dots positioning
type BlueDots = {
  center: { x: number; y: number; z?: number };
  left?: { x: number; y: number; z?: number };
  right?: { x: number; y: number; z?: number };
};

export class GlassesRenderer {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private glassesModel: THREE.Group | null = null
  private loader: GLTFLoader
  private animationId: number | null = null

  // Smoothing properties for stable tracking
  private _smoothedPos = new THREE.Vector3()
  private _targetPos = new THREE.Vector3()
  private _smoothedQuat = new THREE.Quaternion()
  private _targetQuat = new THREE.Quaternion()
  private _targetScale = 1.0
  private _hasInitSmooth = false
  private _debug = true // toggle for debug logging (temporarily enabled)
  private _mirrorX = true // Mirror X coordinates for front camera

  constructor(canvas: HTMLCanvasElement, width: number, height: number) {
    // Initialize Scene
    this.scene = new THREE.Scene()
    this.scene.background = null // Transparent background

    // Initialize Camera
    this.camera = new THREE.PerspectiveCamera(
      50, // Field of view
      width / height, // Aspect ratio
      0.1, // Near clipping plane
      1000 // Far clipping plane
    )
    this.camera.position.z = 5

    // Initialize Renderer
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      alpha: true,
      antialias: true,
      preserveDrawingBuffer: true // For snapshot functionality
    })
    this.renderer.setSize(width, height)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.outputColorSpace = THREE.SRGBColorSpace

    // Enable shadows for better visual quality (optional - can be disabled for performance)
    this.renderer.shadowMap.enabled = true
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap

    // Setup Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
    this.scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4)
    directionalLight.position.set(0, 1, 1)
    directionalLight.castShadow = true // Enable shadow casting
    this.scene.add(directionalLight)

    // Initialize Loader
    this.loader = new GLTFLoader()
  }

  async loadGlassesModel(modelUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Remove existing model if any
      if (this.glassesModel) {
        this.scene.remove(this.glassesModel)
        this.glassesModel = null
      }

      this.loader.load(
        modelUrl,
        (gltf) => {
          this.glassesModel = gltf.scene

          // Set initial orientation to face camera (outward)
          // Default orientation should face the camera, not the eyes
          this.glassesModel.rotation.set(0, 0, 0) // No rotation - face camera directly

          // Set initial properties
          this.glassesModel.traverse((child) => {
            if ((child as THREE.Mesh).isMesh) {
              const mesh = child as THREE.Mesh
              mesh.castShadow = true
              mesh.receiveShadow = true

              // Ensure materials are double-sided for better rendering
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(mat => {
                  mat.side = THREE.DoubleSide
                })
              } else {
                mesh.material.side = THREE.DoubleSide
              }
            }
          })

          this.scene.add(this.glassesModel)
          resolve()
        },
        (progress) => {
          console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%')
        },
        (error) => {
          console.error('Error loading model:', error)
          reject(error)
        }
      )
    })
  }

  updateGlassesPosition(position: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return
    
    this.glassesModel.position.set(position.x, position.y, position.z)
  }

  updateGlassesRotation(rotation: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return

    // Apply face rotation while maintaining camera-facing orientation
    // No base rotation needed - glasses should face camera naturally
    this.glassesModel.rotation.set(
      rotation.x,                    // Pitch (up/down head movement)
      rotation.y,                    // Yaw (left/right head movement) - direct face tracking
      rotation.z                     // Roll (head tilt)
    )
  }

  updateGlassesScale(scale: number) {
    if (!this.glassesModel) return
    
    this.glassesModel.scale.set(scale, scale, scale)
  }

  // Update glasses transform from face landmarks
  updateFromFaceLandmarks(keyPoints: any, rotation: any, scale: number, canvasWidth: number = 640, canvasHeight: number = 480) {
    if (!this.glassesModel || !keyPoints) return

    // Calculate center position between eyes with better accuracy
    const centerX = (keyPoints.leftEyeCenter.x + keyPoints.rightEyeCenter.x) / 2
    const centerY = (keyPoints.leftEyeCenter.y + keyPoints.rightEyeCenter.y) / 2

    // Calculate vertical offset to position glasses properly on nose bridge
    const eyeToNoseOffset = keyPoints.noseBridge.y - centerY
    const adjustedY = centerY + (eyeToNoseOffset * 0.3) // Move slightly towards nose bridge

    // Convert normalized coordinates to Three.js coordinates
    // MediaPipe coordinates are normalized 0-1, need to map to Three.js world space
    const aspectRatio = canvasWidth / canvasHeight

    // Map to Three.js coordinate system with proper aspect ratio handling
    const x = (centerX - 0.5) * 8 * aspectRatio  // Horizontal positioning
    const y = -(adjustedY - 0.5) * 8             // Vertical positioning (inverted Y)
    const z = keyPoints.noseBridge.z ? keyPoints.noseBridge.z * 2 : 0 // Depth positioning

    this.updateGlassesPosition({ x, y, z })
    this.updateGlassesRotation(rotation)
    this.updateGlassesScale(scale)
  }

  // New method for more precise positioning with offset support
  updateFromFaceLandmarksAdvanced(
    keyPoints: any,
    rotation: any,
    scale: number,
    videoElement: HTMLVideoElement,
    offsets: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 }
  ) {
    if (!this.glassesModel || !keyPoints || !videoElement) return

    const videoWidth = videoElement.videoWidth
    const videoHeight = videoElement.videoHeight
    const aspectRatio = videoWidth / videoHeight

    // Calculate glasses center point (slightly above eye center, on nose bridge)
    const leftEye = keyPoints.leftEyeCenter
    const rightEye = keyPoints.rightEyeCenter
    const noseBridge = keyPoints.noseBridge

    // Weighted average for optimal glasses positioning
    const centerX = (leftEye.x + rightEye.x) / 2
    const centerY = (leftEye.y + rightEye.y) / 2

    // Adjust Y position to sit properly on nose bridge
    const eyeToNoseBridge = noseBridge.y - centerY
    const glassesY = centerY + (eyeToNoseBridge * 0.4) // 40% towards nose bridge

    // Convert to Three.js world coordinates
    const baseWorldX = (centerX - 0.5) * 6 * aspectRatio
    const baseWorldY = -(glassesY - 0.5) * 6
    const baseWorldZ = noseBridge.z ? noseBridge.z * 1.5 : 0

    // Apply user offsets (convert from UI range to world coordinates)
    const worldX = baseWorldX + (offsets.x / 100) * 2  // Scale offset to reasonable range
    const worldY = baseWorldY + (offsets.y / 100) * 2  // Scale offset to reasonable range
    const worldZ = baseWorldZ + (offsets.z / 100) * 0.5 // Smaller range for depth

    // Apply position
    this.updateGlassesPosition({ x: worldX, y: worldY, z: worldZ })
    this.updateGlassesRotation(rotation)
    this.updateGlassesScale(scale)
  }

  // Change glasses material/color
  updateGlassesMaterial(options: { 
    frameColor?: string; 
    lensOpacity?: number;
    lensColor?: string;
  }) {
    if (!this.glassesModel) return

    this.glassesModel.traverse((child) => {
      if ((child as THREE.Mesh).isMesh) {
        const mesh = child as THREE.Mesh
        
        // Update frame and lens properties with proper material handling
        if (Array.isArray(mesh.material)) {
          mesh.material.forEach((mat: any) => {
            if (options.frameColor && mesh.name.toLowerCase().includes('frame') && mat.color) {
              mat.color.set(options.frameColor)
            }
            if (mesh.name.toLowerCase().includes('lens')) {
              if (options.lensOpacity !== undefined) {
                mat.opacity = options.lensOpacity
                mat.transparent = true
                mat.needsUpdate = true
              }
              if (options.lensColor && mat.color) {
                mat.color.set(options.lensColor)
              }
            }
          })
        } else {
          const mat: any = mesh.material
          if (options.frameColor && mesh.name.toLowerCase().includes('frame') && mat.color) {
            mat.color.set(options.frameColor)
          }
          if (mesh.name.toLowerCase().includes('lens')) {
            if (options.lensOpacity !== undefined) {
              mat.opacity = options.lensOpacity
              mat.transparent = true
              mat.needsUpdate = true
            }
            if (options.lensColor && mat.color) {
              mat.color.set(options.lensColor)
            }
          }
        }
      }
    })
  }

  render() {
    this.renderer.render(this.scene, this.camera)
  }

  startRenderLoop(callback?: () => void) {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)

      if (callback) callback() // callback updates _targetPos/_targetQuat/_targetScale

      // Apply smoothing in render loop for consistent timing
      if (this.glassesModel && this._hasInitSmooth) {
        const alpha = 0.35 // 0.2–0.5 feels good
        this._smoothedPos.lerp(this._targetPos, alpha)
        this._smoothedQuat.slerp(this._targetQuat, alpha)

        // Apply smoothed transforms to model
        this.glassesModel.position.copy(this._smoothedPos)
        this.glassesModel.quaternion.copy(this._smoothedQuat)
        this.glassesModel.scale.set(this._targetScale, this._targetScale, this._targetScale)

        // Debug smoothing (temporary)
        if (this._debug && Math.random() < 0.01) { // Log 1% of frames to avoid spam
          console.log('🔄 Smoothing applied:', {
            target: { x: this._targetPos.x.toFixed(2), y: this._targetPos.y.toFixed(2), z: this._targetPos.z.toFixed(2) },
            smoothed: { x: this._smoothedPos.x.toFixed(2), y: this._smoothedPos.y.toFixed(2), z: this._smoothedPos.z.toFixed(2) }
          })
        }
      }

      this.render()
    }
    animate()
  }

  stopRenderLoop() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  resize(width: number, height: number) {
    this.camera.aspect = width / height
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(width, height)
    this.renderer.setPixelRatio(window.devicePixelRatio) // Update DPR for sharp rendering
  }

  getSnapshot(): string {
    this.render() // Ensure fresh render
    return this.renderer.domElement.toDataURL('image/png')
  }

  // Helper: convert normalized (0-1) coordinates to world coordinates using camera projection
  private ndcToWorld(u: number, v: number, targetZ: number): THREE.Vector3 {
    // NDC: x∈[-1,1], y∈[-1,1] (note: y NDC is flipped from canvas)
    const ndc = new THREE.Vector3(2 * u - 1, 1 - 2 * v, 0.5)
    // Unproject to world space near the near plane then create ray towards camera
    ndc.unproject(this.camera)
    const dir = ndc.sub(this.camera.position).normalize()

    // Guard: prevent division by zero when ray is parallel to target plane
    const EPS = 1e-6
    if (Math.abs(dir.z) < EPS) {
      // Ray almost parallel to plane; fallback: return projection point without offset
      return new THREE.Vector3(ndc.x, ndc.y, targetZ)
    }

    const t = (targetZ - this.camera.position.z) / dir.z
    return this.camera.position.clone().add(dir.multiplyScalar(t))
  }

  // Helper: convert pixel offset to world delta at target Z
  private pxToWorldDelta(dxPx: number, dyPx: number, targetZ: number): THREE.Vector3 {
    const height = this.renderer.domElement.height
    const width = this.renderer.domElement.width

    const vFov = THREE.MathUtils.degToRad(this.camera.fov)
    const dist = Math.abs(this.camera.position.z - targetZ)
    const worldHeight = 2 * Math.tan(vFov / 2) * dist
    const worldWidth = worldHeight * this.camera.aspect

    const dxWorld = (dxPx / width) * worldWidth
    const dyWorld = (-dyPx / height) * worldHeight // minus because Y screen goes down
    return new THREE.Vector3(dxWorld, dyWorld, 0)
  }

  // Helper: create blue dots from face landmarks
  getBlueDotsFromLandmarks(keyPoints: any): BlueDots {
    // Center = middle of both eyes, pulled slightly towards nose bridge
    const cx = (keyPoints.leftEyeCenter.x + keyPoints.rightEyeCenter.x) / 2
    const cyRaw = (keyPoints.leftEyeCenter.y + keyPoints.rightEyeCenter.y) / 2
    const NOSE_WEIGHT = 0.28 // Reduced from 0.35 to prevent glasses dropping too low
    const cy = cyRaw + ((keyPoints.noseBridge.y - cyRaw) * NOSE_WEIGHT)

    // Face width approximation from eye distance
    const eyeDx = Math.abs(keyPoints.rightEyeCenter.x - keyPoints.leftEyeCenter.x)
    // Move left/right relative to center (tweak 0.9~1.4 according to model)
    const leftX = cx - eyeDx * 0.9
    const rightX = cx + eyeDx * 0.9

    return {
      center: { x: cx, y: cy, z: keyPoints.noseBridge?.z },
      left: { x: Math.max(0, Math.min(1, leftX)), y: cy, z: keyPoints.noseBridge?.z },
      right: { x: Math.max(0, Math.min(1, rightX)), y: cy, z: keyPoints.noseBridge?.z },
    }
  }

  // Helper: get auto scale from eye distance
  getAutoScaleFromEyes(keyPoints: any, baseEyeDist = 0.08, baseScale = 1.0): number {
    const d = Math.hypot(
      keyPoints.rightEyeCenter.x - keyPoints.leftEyeCenter.x,
      keyPoints.rightEyeCenter.y - keyPoints.leftEyeCenter.y
    )
    // baseEyeDist = normalized eye distance when you feel scale=1 is right
    return (d / baseEyeDist) * baseScale
  }

  // Main method: update glasses position using blue dots with camera projection
  updateFromBlueDots(
    blue: BlueDots,
    which: 'center' | 'left' | 'right',
    rotation: { x: number; y: number; z: number },
    scale: number,
    offsets: { x?: number; y?: number; z?: number } = {},
    defaultTargetZ: number = 0 // usually 0 if scene origin is used as face plane
  ) {
    if (!this.glassesModel || !blue) return

    const anchor =
      (which === 'left' && blue.left) ? blue.left :
      (which === 'right' && blue.right) ? blue.right :
      blue.center

    // Determine Z target (plane where glasses are placed)
    // Don't use anchor.z as it's likely normalized from face tracker, not world Z
    const targetZ = this.glassesModel ? this.glassesModel.position.z : defaultTargetZ

    // Apply X mirroring if enabled (for front camera)
    const mirroredU = this._mirrorX ? 1.0 - anchor.x : anchor.x

    // Calculate world position from normalized point
    const world = this.ndcToWorld(mirroredU, anchor.y, targetZ)

    // Apply pixel-based offsets using proper world conversion
    const delta = this.pxToWorldDelta(offsets.x ?? 0, offsets.y ?? 0, targetZ)
    world.add(delta)

    // Depth offset (smaller range for Z)
    world.z += ((offsets.z ?? 0) / 100) * 0.5

    // Set target position and rotation for smoothing (no lerp here)
    this._targetPos.set(world.x, world.y, world.z)
    this._targetQuat.setFromEuler(new THREE.Euler(rotation.x, rotation.y, rotation.z))
    this._targetScale = scale

    // Initialize smoothing on first call
    if (!this._hasInitSmooth) {
      this._smoothedPos.copy(this._targetPos)
      this._smoothedQuat.copy(this._targetQuat)
      this._hasInitSmooth = true

      // Apply immediately on first call
      this.glassesModel.position.copy(this._smoothedPos)
      this.glassesModel.quaternion.copy(this._smoothedQuat)
      this.glassesModel.scale.set(scale, scale, scale)
    }

    // Debug logging (only when debug flag is enabled)
    if (this._debug) {
      console.log('🎯 Blue dots positioning:', {
        anchor: which,
        normalized: { x: anchor.x, y: anchor.y },
        mirrored: { x: this._mirrorX ? 1.0 - anchor.x : anchor.x, y: anchor.y },
        world: { x: world.x.toFixed(2), y: world.y.toFixed(2), z: world.z.toFixed(2) },
        targetSet: true,
        hasInitSmooth: this._hasInitSmooth
      })
    }
  }

  // Convenience method: update from face landmarks using blue dots approach
  updateFromFaceLandmarksWithBlueDots(
    keyPoints: any,
    rotation: { x: number; y: number; z: number },
    baseScale: number = 1.0,
    anchor: 'center' | 'left' | 'right' = 'center',
    offsets: { x?: number; y?: number; z?: number } = {},
    useAutoScale: boolean = true
  ) {
    console.log('🎯 updateFromFaceLandmarksWithBlueDots called:', {
      hasKeyPoints: !!keyPoints,
      hasModel: !!this.glassesModel,
      anchor,
      baseScale,
      useAutoScale
    })

    if (!keyPoints) {
      console.warn('❌ No keyPoints provided')
      return
    }

    if (!this.glassesModel) {
      console.warn('❌ No glasses model loaded')
      return
    }

    // Generate blue dots from landmarks
    const blueDots = this.getBlueDotsFromLandmarks(keyPoints)
    console.log('📍 Generated blue dots:', blueDots)

    // Calculate scale (auto or manual)
    const finalScale = useAutoScale
      ? this.getAutoScaleFromEyes(keyPoints, 0.08, baseScale)
      : baseScale

    console.log('📏 Final scale:', finalScale)

    // Update position using blue dots
    this.updateFromBlueDots(
      blueDots,
      anchor,
      rotation,
      finalScale,
      offsets,
      0 // default target Z
    )

    // Additional debug info (always show for now to debug the issue)
    console.log('👁️ Face landmarks to blue dots:', {
      eyeDistance: Math.hypot(
        keyPoints.rightEyeCenter.x - keyPoints.leftEyeCenter.x,
        keyPoints.rightEyeCenter.y - keyPoints.leftEyeCenter.y
      ).toFixed(3),
      autoScale: useAutoScale ? finalScale.toFixed(2) : 'disabled',
      anchor,
      blueDots: {
        center: `(${blueDots.center.x.toFixed(3)}, ${blueDots.center.y.toFixed(3)})`,
        left: blueDots.left ? `(${blueDots.left.x.toFixed(3)}, ${blueDots.left.y.toFixed(3)})` : 'none',
        right: blueDots.right ? `(${blueDots.right.x.toFixed(3)}, ${blueDots.right.y.toFixed(3)})` : 'none'
      },
      hasModel: !!this.glassesModel,
      hasInitSmooth: this._hasInitSmooth
    })
  }

  // Method to adjust glasses base orientation if needed
  setGlassesBaseOrientation(rotation: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return

    // Set the base orientation of the glasses model
    // This is useful if the 3D model has a different default orientation
    this.glassesModel.rotation.set(rotation.x, rotation.y, rotation.z)

    console.log('🔄 Glasses base orientation set to:', {
      x: rotation.x * (180 / Math.PI), // Convert to degrees for readability
      y: rotation.y * (180 / Math.PI),
      z: rotation.z * (180 / Math.PI)
    })
  }

  // Debug method to test different orientations
  testGlassesOrientation() {
    if (!this.glassesModel) {
      console.log('❌ No glasses model loaded')
      return
    }

    console.log('🧪 Testing glasses orientations...')

    // Test different orientations - focus on camera-facing options
    const orientations = [
      { name: 'Default (Camera Facing)', x: 0, y: 0, z: 0 },
      { name: 'Rotated 180° (Eye Facing)', x: 0, y: Math.PI, z: 0 },
      { name: 'Rotated 90° Right', x: 0, y: Math.PI/2, z: 0 },
      { name: 'Rotated 90° Left', x: 0, y: -Math.PI/2, z: 0 },
      { name: 'Tilted Up 90°', x: Math.PI/2, y: 0, z: 0 },
      { name: 'Tilted Down 90°', x: -Math.PI/2, y: 0, z: 0 }
    ]

    let currentTest = 0
    const testInterval = setInterval(() => {
      if (currentTest >= orientations.length) {
        clearInterval(testInterval)
        // Reset to camera-facing orientation
        this.setGlassesBaseOrientation({ x: 0, y: 0, z: 0 })
        console.log('✅ Orientation test complete - reset to camera-facing')
        return
      }

      const orientation = orientations[currentTest]
      console.log(`🔄 Testing: ${orientation.name}`)
      this.setGlassesBaseOrientation(orientation)
      currentTest++
    }, 2000) // Change orientation every 2 seconds
  }

  // Toggle debug logging
  setDebugMode(enabled: boolean) {
    this._debug = enabled
    console.log(`🐛 Debug mode ${enabled ? 'enabled' : 'disabled'}`)
  }

  // Toggle X-axis mirroring for front camera
  setMirrorX(enabled: boolean) {
    this._mirrorX = enabled
    console.log(`🪞 X-axis mirroring ${enabled ? 'enabled' : 'disabled'}`)
  }

  // Get current X mirroring state
  getMirrorX(): boolean {
    return this._mirrorX
  }

  dispose() {
    this.stopRenderLoop()
    
    if (this.glassesModel) {
      this.scene.remove(this.glassesModel)
      this.glassesModel = null
    }
    
    this.renderer.dispose()
    this.scene.clear()
  }
}