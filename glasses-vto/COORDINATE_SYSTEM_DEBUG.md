# Coordinate System Debug

## Problem Analysis from Screenshots

Dari screenshot yang <PERSON><PERSON> berikan, terlihat bahwa:

1. **Screenshot 1**: <PERSON><PERSON> menoleh ke kiri, tapi kacamata malah di sebelah kiri jauh dari wajah
2. **Screenshot 2**: <PERSON><PERSON> menoleh ke kanan, tapi kacamata malah di sebelah kanan jauh dari wajah

Ini menunjukkan ada masalah fundamental dengan coordinate system mapping.

## Current State (After Reset)

Saya telah me-reset semua mirroring ke state default untuk debugging:

```typescript
// Disabled all mirroring temporarily
private _mirrorX = false                    // No X mirroring
private mirrorCompensation = false          // No rotation mirroring  
const yaw = Math.atan2(noseOffset, eyeDistance) * 1.5  // Raw yaw
```

## Debug Commands

### 1. Test Raw Movement (No Mirroring)
```javascript
window.debugGlasses.testRawMovement()
```

**Expected behavior dengan no mirroring**:
- `x=0.2` → Kacamata di LEFT side of screen
- `x=0.8` → Kacamata di RIGHT side of screen
- `x=0.5` → Kacamata di CENTER

### 2. Check Current Settings
```javascript
console.log('Settings:', {
  mirrorX: glassesRendererRef.current?.getMirrorX(),
  mirrorCompensation: faceDetectorRef.current?.getMirrorCompensation()
})
```

### 3. Monitor Coordinate Flow
Look for this debug output:
```javascript
🎯 Blue dots positioning: {
  normalized: { x: "0.500", y: "0.400" },    // Input coordinates
  mirrored: { x: "0.500", y: "0.400" },      // After X mirroring (should be same now)
  world: { x: "0.00", y: "-1.13", z: "0.00" }, // World coordinates
  settings: { mirrorX: false, targetZ: "0.00" }
}
```

## Understanding the Coordinate Systems

### 1. MediaPipe Normalized Coordinates (0-1)
```
(0,0) -------- (1,0)
  |              |
  |    (0.5,0.5) |  <- Center
  |              |
(0,1) -------- (1,1)
```

### 2. Three.js World Coordinates
```
(-X) -------- (+X)
  |              |
  |    (0,0,0)   |  <- Center
  |              |
(-X) -------- (+X)
```

### 3. Camera View (Mirror Effect)
```
User sees:
LEFT  -------- RIGHT
  |              |
  |    CENTER    |
  |              |
LEFT  -------- RIGHT

But in world coordinates:
RIGHT -------- LEFT  (flipped)
  |              |
  |    CENTER    |
  |              |
RIGHT -------- LEFT
```

## Testing Strategy

### Step 1: Test Raw Coordinates (No Mirroring)
```javascript
// This should help us understand the base coordinate system
window.debugGlasses.testRawMovement()
```

**What to observe**:
- Does `x=0.2` put glasses on LEFT side of screen?
- Does `x=0.8` put glasses on RIGHT side of screen?
- Are the world coordinates making sense?

### Step 2: Test Real Face Movement
Move your head and observe console output:
```javascript
🎯 Blue dots positioning: {
  normalized: { x: "???", y: "???" },  // What values do you see?
  world: { x: "???", y: "???" }        // Where does this put the glasses?
}
```

### Step 3: Determine Correct Mirroring
Based on Step 1 & 2 results, we'll determine:
- Do we need X mirroring? (`_mirrorX = true/false`)
- Do we need rotation mirroring? (`mirrorCompensation = true/false`)
- Do we need to negate yaw? (`yaw = -Math.atan2(...)`)

## Expected Debug Output

### When Working Correctly
```javascript
// Head in center
🎯 Blue dots positioning: {
  normalized: { x: "0.500", y: "0.400" },
  world: { x: "0.00", y: "-1.13", z: "0.00" }  // Center
}

// Head moved right
🎯 Blue dots positioning: {
  normalized: { x: "0.700", y: "0.400" },      // Higher X = right
  world: { x: "1.20", y: "-1.13", z: "0.00" }  // Positive X = right in world
}

// Head moved left  
🎯 Blue dots positioning: {
  normalized: { x: "0.300", y: "0.400" },      // Lower X = left
  world: { x: "-1.20", y: "-1.13", z: "0.00" } // Negative X = left in world
}
```

### When Mirroring Needed
If the above shows glasses moving opposite to head movement, then we need mirroring.

## Next Steps

1. **Run the debug command**:
   ```javascript
   window.debugGlasses.testRawMovement()
   ```

2. **Observe the behavior**:
   - Does `x=0.2` put glasses on left?
   - Does `x=0.8` put glasses on right?

3. **Test real head movement**:
   - Move head right, check normalized coordinates
   - Move head left, check normalized coordinates

4. **Report results**:
   - Tell me what you observe
   - Share the console output
   - Describe where glasses appear vs where they should be

Based on your observations, I'll determine the correct mirroring settings to make the movement natural.

## Possible Outcomes

### Outcome A: Raw coordinates work correctly
- `x=0.2` → glasses left
- `x=0.8` → glasses right
- **Solution**: Enable appropriate mirroring for selfie view

### Outcome B: Raw coordinates are inverted
- `x=0.2` → glasses right (wrong)
- `x=0.8` → glasses left (wrong)  
- **Solution**: Fix base coordinate mapping

### Outcome C: Coordinates work but face tracking is inverted
- Raw test works fine
- But real head movement gives wrong coordinates
- **Solution**: Fix face detection coordinate interpretation

Let's start with the debug test and see what we discover!
