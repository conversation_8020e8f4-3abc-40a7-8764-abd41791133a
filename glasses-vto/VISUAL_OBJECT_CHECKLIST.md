# Visual Object Test - Complete Checklist

## 🎯 Quick Visual Test Commands

### **Immediate Visibility Tests** (Run in Browser Console):

#### 1. **Test Red Cube** (Most Visible):
```javascript
window.debugGlasses.testCube()
```
**Expected**: Bright red cube appears for 3 seconds at screen center

#### 2. **Test Simple Glasses** (No GLTF Loading):
```javascript
window.debugGlasses.testSimpleGlasses()
```
**Expected**: Simple glasses shape (2 circles + bridge) appears

#### 3. **Test Center Position**:
```javascript
window.debugGlasses.testCenter()
```
**Expected**: Current model positioned at screen center

#### 4. **Test Manual Positioning**:
```javascript
window.debugGlasses.testManual(320, 0, -100, 2)
```
**Expected**: Object at center with appropriate scale

## 🔍 Visual Inspection Checklist

### **Step 1: Basic Setup**
- [ ] Open `http://localhost:3001/debug`
- [ ] Click "Start Camera" - video feed should appear
- [ ] Video should be mirrored (selfie view)
- [ ] No console errors in browser dev tools

### **Step 2: Model Loading Test**
- [ ] Click "Test Model Load" for each model
- [ ] All models should show "✅ Available" status
- [ ] No 404 errors in network tab

### **Step 3: Object Visibility Test**
- [ ] Run `window.debugGlasses.testCube()` in console
- [ ] **CRITICAL**: Red cube should be clearly visible for 3 seconds
- [ ] If no cube visible → WebGL or rendering issue

### **Step 4: Simple Glasses Test**
- [ ] Run `window.debugGlasses.testSimpleGlasses()` in console
- [ ] Should see simple glasses shape (2 circles + bridge)
- [ ] If visible → Three.js rendering works, GLTF loading might be issue

### **Step 5: Face Detection Test**
- [ ] Click "Debug ON" to enable debug mode
- [ ] Should see blue dots on face (MediaPipe landmarks)
- [ ] If no blue dots → MediaPipe face detection issue

### **Step 6: Object Tracking Test**
- [ ] With face detected, object should follow face movement
- [ ] Object should stay aligned with face center
- [ ] Scale should be appropriate (not too big/small)

## 🚨 Troubleshooting by Symptoms

### **Symptom: No Object Visible At All**

#### Possible Causes & Solutions:

1. **WebGL Not Working**:
   ```javascript
   // Test WebGL support
   const canvas = document.createElement('canvas')
   const gl = canvas.getContext('webgl')
   console.log('WebGL supported:', !!gl)
   ```

2. **Object Outside Camera View**:
   ```javascript
   // Check camera and object positions
   console.log('Camera:', window.debugGlasses.getCamera())
   console.log('Object:', window.debugGlasses.getGlasses())
   ```

3. **Scale Too Small**:
   ```javascript
   // Test with larger scale
   window.debugGlasses.testManual(320, 0, -100, 10)
   ```

4. **Z-Position Too Far/Close**:
   ```javascript
   // Test different Z positions
   window.debugGlasses.testManual(320, 0, -50, 2)   // Very close
   window.debugGlasses.testManual(320, 0, -200, 2)  // Far
   ```

### **Symptom: Red Cube Visible, But No Glasses**

#### This means Three.js works, but GLTF loading fails:

1. **Check Model Files**:
   ```javascript
   // Test model availability
   fetch('/models/default-glasses.gltf').then(r => console.log('Model status:', r.status))
   ```

2. **Use Simple Glasses**:
   ```javascript
   // Bypass GLTF loading
   window.debugGlasses.testSimpleGlasses()
   ```

3. **Check Console for GLTF Errors**:
   - Look for "Error loading model" messages
   - Check for missing texture files

### **Symptom: Object Visible But Wrong Size**

#### Scale Issues:

1. **Too Large**:
   ```javascript
   // Test smaller scales
   window.debugGlasses.testManual(320, 0, -100, 0.5)
   window.debugGlasses.testManual(320, 0, -100, 1.0)
   ```

2. **Too Small**:
   ```javascript
   // Test larger scales
   window.debugGlasses.testManual(320, 0, -100, 5)
   window.debugGlasses.testManual(320, 0, -100, 10)
   ```

### **Symptom: Object Visible But Wrong Position**

#### Positioning Issues:

1. **Not Following Face**:
   ```javascript
   // Check face detection
   window.debugGlasses.toggleDebug()  // Enable debug logs
   // Look for face detection logs in console
   ```

2. **Mirroring Issues**:
   ```javascript
   // Test different X positions
   window.debugGlasses.testManual(100, 0, -100, 2)   // Left
   window.debugGlasses.testManual(320, 0, -100, 2)   // Center  
   window.debugGlasses.testManual(540, 0, -100, 2)   // Right
   ```

## 📊 Expected Debug Console Output

### **Successful Object Loading**:
```
📦 GLTF loaded successfully: {scene: Group, ...}
👓 Glasses model info: {children: 1, position: Vector3, ...}
✅ Processed 1 meshes, adding to scene
🧪 TEST: Positioned glasses at center for visibility test
```

### **Successful Test Cube**:
```
🧪 Testing simple geometry render...
🧪 Test cube added at: Vector3 {x: 320, y: -240, z: -300}
🧪 Scene children count: 2
🧪 Test cube rendered successfully
🧪 Test cube removed
```

### **Successful Simple Glasses**:
```
🧪 Rendering simple glasses shape...
✅ Simple glasses shape created and positioned
👓 Simple glasses visible at center
```

## ✅ Success Criteria

### **Minimum Working System**:
- [ ] Red test cube visible when commanded
- [ ] Simple glasses shape visible when commanded
- [ ] Camera feed working with mirror effect
- [ ] No critical console errors

### **Full Working System**:
- [ ] GLTF glasses models load successfully
- [ ] Face detection shows blue dots
- [ ] Glasses follow face movement accurately
- [ ] Appropriate scale and positioning
- [ ] Smooth performance (>15 FPS)

### **Production Ready System**:
- [ ] Multiple glasses models work
- [ ] Accurate face tracking
- [ ] Good performance on mobile
- [ ] Error handling for edge cases
- [ ] User-friendly interface

## 🎮 Interactive Test Sequence

### **Run This Complete Test** (Copy-paste in console):
```javascript
console.log('🧪 Starting complete visual object test...')

// Test 1: Basic visibility
console.log('Test 1: Red cube visibility')
window.debugGlasses.testCube()

setTimeout(() => {
  // Test 2: Simple glasses
  console.log('Test 2: Simple glasses shape')
  window.debugGlasses.testSimpleGlasses()
}, 4000)

setTimeout(() => {
  // Test 3: Center positioning
  console.log('Test 3: Center positioning')
  window.debugGlasses.testCenter()
}, 8000)

setTimeout(() => {
  // Test 4: Manual positioning
  console.log('Test 4: Manual positioning')
  window.debugGlasses.testManual(320, 0, -100, 2)
}, 12000)

setTimeout(() => {
  // Test 5: Get system info
  console.log('Test 5: System info')
  console.log('Camera:', window.debugGlasses.getCamera())
  console.log('Object:', window.debugGlasses.getGlasses())
  console.log('Stats:', window.debugGlasses.getStats())
  console.log('🎉 Visual object test complete!')
}, 16000)
```

## 📝 Test Results Template

```
VISUAL OBJECT TEST RESULTS:
==========================

✅/❌ Red cube visible: ___
✅/❌ Simple glasses visible: ___
✅/❌ GLTF model loads: ___
✅/❌ Face detection works: ___
✅/❌ Object follows face: ___
✅/❌ Appropriate scale: ___
✅/❌ Smooth performance: ___

Issues found:
- _______________
- _______________

Next steps:
- _______________
- _______________
```

**Run the tests and fill out this template to track progress!** 🎯
