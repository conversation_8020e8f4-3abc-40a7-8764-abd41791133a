# Blue Dots Payload Examples

## Overview
Dokumentasi ini menunjukkan contoh payload "blue dots" asli dari face tracker MediaPipe dan bagaimana data tersebut diproses untuk positioning kacamata.

## MediaPipe Face Landmarks Payload

### Raw MediaPipe Output
```javascript
// MediaPipe FaceLandmarkerResult
{
  faceLandmarks: [
    [
      // Array of 468 landmarks, each with x, y, z coordinates
      { x: 0.123, y: 0.456, z: -0.012 }, // landmark 0
      { x: 0.234, y: 0.567, z: -0.023 }, // landmark 1
      // ... 466 more landmarks
    ]
  ],
  faceBlendshapes: [...],
  facialTransformationMatrixes: [...]
}
```

### Processed KeyPoints from getFaceKeyPoints()
```javascript
// Output dari getFaceKeyPoints(landmarks)
{
  // Eye corners for width calculation
  leftEyeOuter: { x: 0.234, y: 0.378, z: -0.045 },    // landmark 33
  rightEyeOuter: { x: 0.766, y: 0.382, z: -0.048 },   // landmark 263
  leftEyeInner: { x: 0.345, y: 0.375, z: -0.042 },    // landmark 133
  rightEyeInner: { x: 0.655, y: 0.379, z: -0.044 },   // landmark 362

  // Eye centers for positioning (calculated or from landmarks 468/473)
  leftEyeCenter: { x: 0.289, y: 0.376, z: -0.043 },   // calculated center
  rightEyeCenter: { x: 0.711, y: 0.380, z: -0.046 },  // calculated center

  // Eye positioning points
  leftEyeTop: { x: 0.289, y: 0.356, z: -0.041 },      // landmark 159
  rightEyeTop: { x: 0.711, y: 0.360, z: -0.044 },     // landmark 386
  leftEyeBottom: { x: 0.289, y: 0.396, z: -0.045 },   // landmark 145
  rightEyeBottom: { x: 0.711, y: 0.400, z: -0.048 },  // landmark 374

  // Nose bridge for vertical positioning
  noseBridge: { x: 0.500, y: 0.345, z: -0.038 },      // landmark 6
  noseTip: { x: 0.500, y: 0.456, z: -0.012 },         // landmark 1
  noseCenter: { x: 0.500, y: 0.400, z: -0.025 },      // landmark 168

  // Face outline for rotation calculation
  chin: { x: 0.500, y: 0.678, z: -0.055 },            // landmark 152
  forehead: { x: 0.500, y: 0.123, z: -0.048 },        // landmark 10

  // Temples for arms positioning
  leftTemple: { x: 0.156, y: 0.345, z: -0.067 },      // landmark 54
  rightTemple: { x: 0.844, y: 0.349, z: -0.069 }      // landmark 284
}
```

## Generated Blue Dots from getBlueDotsFromLandmarks()

### Input KeyPoints Example
```javascript
const keyPoints = {
  leftEyeCenter: { x: 0.289, y: 0.376, z: -0.043 },
  rightEyeCenter: { x: 0.711, y: 0.380, z: -0.046 },
  noseBridge: { x: 0.500, y: 0.345, z: -0.038 }
}
```

### Generated Blue Dots Output
```javascript
// Output dari getBlueDotsFromLandmarks(keyPoints)
{
  center: { 
    x: 0.500,      // (0.289 + 0.711) / 2 = 0.500
    y: 0.389,      // 0.378 + ((0.345 - 0.378) * 0.35) = 0.366
    z: -0.038      // from noseBridge.z
  },
  left: { 
    x: 0.122,      // 0.500 - (0.422 * 0.9) = 0.120
    y: 0.389,      // same as center
    z: -0.038      // from noseBridge.z
  },
  right: { 
    x: 0.878,      // 0.500 + (0.422 * 0.9) = 0.880
    y: 0.389,      // same as center
    z: -0.038      // from noseBridge.z
  }
}
```

### Calculation Details
```javascript
// Center calculation
const cx = (keyPoints.leftEyeCenter.x + keyPoints.rightEyeCenter.x) / 2
// cx = (0.289 + 0.711) / 2 = 0.500

const cyRaw = (keyPoints.leftEyeCenter.y + keyPoints.rightEyeCenter.y) / 2
// cyRaw = (0.376 + 0.380) / 2 = 0.378

const cy = cyRaw + ((keyPoints.noseBridge.y - cyRaw) * 0.35)
// cy = 0.378 + ((0.345 - 0.378) * 0.35) = 0.378 + (-0.0115) = 0.366

// Eye distance for left/right positioning
const eyeDx = Math.abs(keyPoints.rightEyeCenter.x - keyPoints.leftEyeCenter.x)
// eyeDx = Math.abs(0.711 - 0.289) = 0.422

// Left/right anchor points
const leftX = cx - eyeDx * 0.9   // 0.500 - (0.422 * 0.9) = 0.120
const rightX = cx + eyeDx * 0.9  // 0.500 + (0.422 * 0.9) = 0.880
```

## World Coordinates Conversion

### NDC to World Conversion Example
```javascript
// Input: normalized coordinates (0-1)
const normalized = { x: 0.500, y: 0.389 }

// NDC conversion (normalized device coordinates)
const ndcX = 2 * 0.500 - 1 = 0.0    // center X
const ndcY = 1 - 2 * 0.389 = 0.222  // flipped Y

// Camera projection to world coordinates (example with camera at z=5, targetZ=0)
const worldPosition = ndcToWorld(0.500, 0.389, 0)
// Result: { x: 0.0, y: 1.2, z: 0.0 } (example values)
```

## Debug Output Examples

### Console Output with Debug Enabled
```javascript
// Enable debug mode
window.debugGlasses.enableDebug()

// Example debug output
👁️ Face landmarks to blue dots: {
  eyeDistance: "0.422",
  autoScale: "1.23",
  anchor: "center",
  blueDots: {
    center: "(0.500, 0.366)",
    left: "(0.120, 0.366)",
    right: "(0.880, 0.366)"
  }
}

🎯 Blue dots positioning: {
  anchor: "center",
  normalized: { x: 0.500, y: 0.366 },
  world: { x: "0.00", y: "1.20", z: "0.00" }
}
```

## Testing Different Anchor Points

### Test Center Anchor
```javascript
window.debugGlasses.testBlueDots('center')
// Uses center blue dot: { x: 0.500, y: 0.366 }
```

### Test Left Anchor
```javascript
window.debugGlasses.testBlueDots('left')
// Uses left blue dot: { x: 0.120, y: 0.366 }
```

### Test Right Anchor
```javascript
window.debugGlasses.testBlueDots('right')
// Uses right blue dot: { x: 0.880, y: 0.366 }
```

## Coordinate System Summary

### MediaPipe Normalized Coordinates
- **Range**: 0.0 - 1.0
- **Origin**: Top-left corner (0,0)
- **X**: 0 = left, 1 = right
- **Y**: 0 = top, 1 = bottom
- **Z**: Negative values (depth from camera plane)

### Three.js World Coordinates
- **Range**: Depends on camera setup and target Z
- **Origin**: Camera-relative
- **X**: Negative = left, Positive = right
- **Y**: Negative = down, Positive = up
- **Z**: Negative = away from camera, Positive = towards camera

### Blue Dots Positioning Logic
1. **Center**: Average of left and right eye centers, adjusted towards nose bridge
2. **Left**: Center X minus eye distance * 0.9, same Y as center
3. **Right**: Center X plus eye distance * 0.9, same Y as center

## Performance Considerations

### Smoothing Parameters
```javascript
const alpha = 0.35  // Smoothing factor (0.2-0.5 recommended)
// Lower = smoother but slower response
// Higher = faster response but more jitter
```

### Debug Mode Impact
- Debug logging disabled by default for performance
- Enable only during development/testing
- Use `window.debugGlasses.enableDebug()` to activate

## Troubleshooting Common Issues

### Blue Dots Not Aligning
1. Check eye distance calculation
2. Verify nose bridge positioning factor (0.35)
3. Adjust left/right spread factor (0.9)

### Jittery Movement
1. Increase smoothing alpha (up to 0.5)
2. Check face detection stability
3. Verify landmark quality

### Incorrect Depth
1. Don't use normalized Z as world Z
2. Use constant target Z plane
3. Implement proper depth pipeline if needed
