# Mirror Compensation Fix

## Problem
Ketika menggunakan kamera selfie yang di-mirror (flip horizontal), rotasi kacamata 3D menjadi terbalik:
- Miringkan kepala ke kanan → kacamata miring ke kiri
- Putar kepala ke kiri → kacamata putar ke kanan
- Efek ini terjadi karena video di-mirror tapi rotasi tidak dikompensasi

## Root Cause
```tsx
// Video dan overlay canvas di-mirror
<video style={{ transform: 'scaleX(-1)' }} />
<canvas style={{ transform: 'scaleX(-1)' }} />

// Tapi rotasi face tracking tidak dikompensasi untuk mirroring
```

## Solution Implemented

### 1. **Mirror Compensation in Face Detection**
```typescript
export class FaceDetection {
  private mirrorCompensation = true // Default enabled for selfie camera
  
  calculateFaceRotation(keyPoints: any) {
    // Calculate raw rotations
    const pitch = ... // Up/down (not affected by mirroring)
    const yaw = ...   // Left/right (affected by mirroring)
    const roll = ...  // Tilt (affected by mirroring)
    
    // Apply mirror compensation
    const mirrorMultiplier = this.mirrorCompensation ? -1 : 1
    
    return {
      x: pitch,                        // Pitch: not affected by mirroring
      y: yaw * mirrorMultiplier,       // Yaw: compensated for mirroring
      z: roll * mirrorMultiplier       // Roll: compensated for mirroring
    }
  }
}
```

### 2. **Configurable Mirror Compensation**
```typescript
// Toggle mirror compensation
setMirrorCompensation(enabled: boolean)

// Get current state
getMirrorCompensation(): boolean
```

### 3. **Debug Functions**
```javascript
// Toggle mirror compensation
window.debugGlasses.toggleMirrorCompensation()

// Enable mirror compensation (for selfie camera)
window.debugGlasses.enableMirrorCompensation()

// Disable mirror compensation (for rear camera)
window.debugGlasses.disableMirrorCompensation()
```

## How It Works

### Without Mirror Compensation
```
User tilts head RIGHT → Face tracking detects RIGHT tilt → Glasses tilt RIGHT
But video is mirrored, so user sees glasses tilting LEFT (wrong!)
```

### With Mirror Compensation
```
User tilts head RIGHT → Face tracking detects RIGHT tilt → Apply -1 multiplier → Glasses tilt LEFT
Video is mirrored, so user sees glasses tilting RIGHT (correct!)
```

## Affected Rotations

### Pitch (X-axis) - Up/Down
- ✅ **Not affected** by mirroring
- No compensation needed
- Up movement stays up, down stays down

### Yaw (Y-axis) - Left/Right
- ❌ **Affected** by mirroring
- ✅ **Compensated** with -1 multiplier
- Left turn becomes right turn in mirror view

### Roll (Z-axis) - Tilt
- ❌ **Affected** by mirroring  
- ✅ **Compensated** with -1 multiplier
- Right tilt becomes left tilt in mirror view

## Testing

### Test Mirror Compensation
1. **Enable mirror compensation** (default):
   ```javascript
   window.debugGlasses.enableMirrorCompensation()
   ```

2. **Tilt head to the right**:
   - Expected: Glasses should tilt to the right in mirror view
   - Without compensation: Glasses would tilt left (wrong)

3. **Turn head to the left**:
   - Expected: Glasses should turn left in mirror view
   - Without compensation: Glasses would turn right (wrong)

### Test Without Mirror Compensation
1. **Disable mirror compensation**:
   ```javascript
   window.debugGlasses.disableMirrorCompensation()
   ```

2. **Test same movements**:
   - Rotations will be inverted (wrong for mirror view)
   - Use this for rear camera or non-mirrored setups

### Toggle Test
```javascript
// Quick toggle to see the difference
window.debugGlasses.toggleMirrorCompensation()
```

## Configuration

### Default Settings
- **Mirror compensation**: `enabled` (for selfie camera)
- **Applies to**: Yaw and Roll rotations
- **Not applied to**: Pitch rotation

### When to Disable
- Using rear camera (not mirrored)
- Custom camera setup without mirroring
- Testing raw face tracking data

### When to Enable
- Using front/selfie camera (mirrored)
- Standard selfie view setup
- User expects natural mirror behavior

## Implementation Details

### Face Detection Changes
```typescript
// Before (wrong for mirrored camera)
return {
  x: pitch,
  y: yaw,
  z: roll
}

// After (compensated for mirrored camera)
const mirrorMultiplier = this.mirrorCompensation ? -1 : 1
return {
  x: pitch,                        // No compensation needed
  y: yaw * mirrorMultiplier,       // Compensated
  z: roll * mirrorMultiplier       // Compensated
}
```

### CSS Mirroring
```css
/* Video and overlay both mirrored */
video { transform: scaleX(-1); }
canvas { transform: scaleX(-1); }
```

## Troubleshooting

### Glasses Still Rotating Wrong Way
1. Check if mirror compensation is enabled:
   ```javascript
   console.log('Mirror compensation:', faceDetectorRef.current?.getMirrorCompensation())
   ```

2. Toggle compensation:
   ```javascript
   window.debugGlasses.toggleMirrorCompensation()
   ```

### Rotations Too Sensitive/Insensitive
- Adjust sensitivity multipliers in `calculateFaceRotation()`
- Current values: yaw × 1.5, pitch × 0.8

### Different Camera Setup
- For rear camera: disable mirror compensation
- For custom mirroring: adjust multiplier logic

## Console Output

### Mirror Compensation Enabled
```
🪞 Mirror compensation enabled
🎯 Face rotation: {x: 0.1, y: -0.2, z: -0.15} (compensated)
```

### Mirror Compensation Disabled  
```
🪞 Mirror compensation disabled
🎯 Face rotation: {x: 0.1, y: 0.2, z: 0.15} (raw)
```

## Summary

✅ **Fixed**: Inverted rotations in mirrored camera view
✅ **Added**: Configurable mirror compensation
✅ **Added**: Debug functions for testing
✅ **Maintained**: Natural selfie camera behavior

The glasses now rotate correctly in the mirrored selfie view, matching user expectations for natural mirror behavior.
