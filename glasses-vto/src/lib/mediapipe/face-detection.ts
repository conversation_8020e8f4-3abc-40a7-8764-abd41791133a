import { FaceLandmarker, FilesetResolver, FaceLandmarkerResult } from '@mediapipe/tasks-vision'

export class FaceDetection {
  private faceLandmarker: FaceLandmarker | null = null
  private isInitialized = false
  private lastVideoTime = -1
  private mirrorCompensation = true // Compensate for camera mirroring

  async initialize() {
    if (this.isInitialized) return

    try {
      const vision = await FilesetResolver.forVisionTasks(
        'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm'
      )

      this.faceLandmarker = await FaceLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',
          delegate: 'GPU'
        },
        outputFaceBlendshapes: true,
        runningMode: 'VIDEO',
        numFaces: 1
      })

      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize MediaPipe:', error)
      throw error
    }
  }

  detectFaces(video: HTMLVideoElement): FaceLandmarkerResult | null {
    if (!this.faceLandmarker || !this.isInitialized) {
      console.warn('Face detector not initialized')
      return null
    }

    const timestamp = performance.now()
    
    // Skip if same frame
    if (timestamp <= this.lastVideoTime) {
      return null
    }
    
    this.lastVideoTime = timestamp

    try {
      const results = this.faceLandmarker.detectForVideo(video, timestamp)
      return results
    } catch (error) {
      console.error('Face detection error:', error)
      return null
    }
  }

  // Get key face points for glasses positioning
  getFaceKeyPoints(landmarks: any) {
    if (!landmarks || landmarks.length === 0) return null

    const face = landmarks[0]

    return {
      // Eye corners for width calculation
      leftEyeOuter: face[33],   // Left eye outer corner
      rightEyeOuter: face[263], // Right eye outer corner
      leftEyeInner: face[133],  // Left eye inner corner
      rightEyeInner: face[362], // Right eye inner corner

      // Eye centers for positioning (more accurate points)
      leftEyeCenter: face[468] || this.calculateEyeCenter(face, 'left'), // Left eye center
      rightEyeCenter: face[473] || this.calculateEyeCenter(face, 'right'), // Right eye center

      // Better eye positioning points
      leftEyeTop: face[159],    // Left eye top
      rightEyeTop: face[386],   // Right eye top
      leftEyeBottom: face[145], // Left eye bottom
      rightEyeBottom: face[374], // Right eye bottom

      // Nose bridge for vertical positioning
      noseBridge: face[6],      // Nose bridge top
      noseTip: face[1],         // Nose tip
      noseCenter: face[168],    // Nose center

      // Face outline for rotation calculation
      chin: face[152],          // Chin bottom
      forehead: face[10],       // Forehead center

      // Temples for arms positioning
      leftTemple: face[54],     // Left temple
      rightTemple: face[284],   // Right temple

      // Additional reference points
      leftCheek: face[234],     // Left cheek
      rightCheek: face[454],    // Right cheek
    }
  }

  // Calculate eye center if not available in landmarks
  private calculateEyeCenter(face: any, eye: 'left' | 'right') {
    if (eye === 'left') {
      // Average of key left eye points
      const points = [face[33], face[7], face[163], face[144], face[145], face[153]]
      return this.averagePoints(points)
    } else {
      // Average of key right eye points
      const points = [face[362], face[398], face[384], face[385], face[386], face[387]]
      return this.averagePoints(points)
    }
  }

  // Helper to average multiple points
  private averagePoints(points: any[]) {
    const sum = points.reduce((acc, point) => ({
      x: acc.x + point.x,
      y: acc.y + point.y,
      z: acc.z + (point.z || 0)
    }), { x: 0, y: 0, z: 0 })

    return {
      x: sum.x / points.length,
      y: sum.y / points.length,
      z: sum.z / points.length
    }
  }

  // Calculate face rotation angles for glasses positioning
  calculateFaceRotation(keyPoints: any) {
    if (!keyPoints) return { x: 0, y: 0, z: 0 }

    // Calculate yaw (left-right head rotation)
    // Use eye positions to determine face orientation
    const eyeDistance = Math.sqrt(
      Math.pow(keyPoints.rightEyeOuter.x - keyPoints.leftEyeOuter.x, 2) +
      Math.pow(keyPoints.rightEyeOuter.y - keyPoints.leftEyeOuter.y, 2)
    )

    const centerX = (keyPoints.leftEyeOuter.x + keyPoints.rightEyeOuter.x) / 2
    const noseOffset = keyPoints.noseBridge.x - centerX
    // Reduce yaw sensitivity for more stable glasses positioning
    const yaw = Math.atan2(noseOffset, eyeDistance) * 1.5

    // Calculate pitch (up-down head rotation)
    // Use forehead to chin distance for pitch calculation
    const faceHeight = Math.abs(keyPoints.forehead.y - keyPoints.chin.y)
    const noseHeight = keyPoints.noseBridge.y - keyPoints.forehead.y
    // Adjust pitch to keep glasses naturally positioned
    const pitch = (Math.atan2(noseHeight, faceHeight) - 0.3) * 0.8

    // Calculate roll (head tilt)
    // Use eye line angle for natural tilt following
    const eyeAngle = Math.atan2(
      keyPoints.rightEyeOuter.y - keyPoints.leftEyeOuter.y,
      keyPoints.rightEyeOuter.x - keyPoints.leftEyeOuter.x
    )

    // Return rotation values optimized for forward-facing glasses
    // Apply mirror compensation if enabled (for selfie camera view)
    const mirrorMultiplier = this.mirrorCompensation ? -1 : 1

    const result = {
      x: pitch,                        // Pitch: up/down head movement (not affected by mirroring)
      y: yaw * mirrorMultiplier,       // Yaw: left/right head movement (compensated for mirroring)
      z: eyeAngle * mirrorMultiplier   // Roll: head tilt (compensated for mirroring)
    }

    // Debug rotation (temporary)
    if (Math.random() < 0.02) { // Log 2% of frames
      console.log('🔄 Face rotation calculated:', {
        raw: { pitch: pitch.toFixed(3), yaw: yaw.toFixed(3), roll: eyeAngle.toFixed(3) },
        final: { x: result.x.toFixed(3), y: result.y.toFixed(3), z: result.z.toFixed(3) },
        mirrorCompensation: this.mirrorCompensation,
        noseOffset: noseOffset.toFixed(3),
        eyeDistance: eyeDistance.toFixed(3)
      })
    }

    return result
  }

  // Calculate glasses scale based on face dimensions
  calculateGlassesScale(keyPoints: any) {
    if (!keyPoints) return 1

    // Calculate eye distance (interpupillary distance)
    const eyeDistance = Math.sqrt(
      Math.pow(keyPoints.rightEyeCenter.x - keyPoints.leftEyeCenter.x, 2) +
      Math.pow(keyPoints.rightEyeCenter.y - keyPoints.leftEyeCenter.y, 2)
    )

    // Calculate face width for better scaling
    const faceWidth = Math.sqrt(
      Math.pow(keyPoints.rightCheek.x - keyPoints.leftCheek.x, 2) +
      Math.pow(keyPoints.rightCheek.y - keyPoints.leftCheek.y, 2)
    )

    // Use both eye distance and face width for more accurate scaling
    // Typical eye distance in normalized coords is around 0.15-0.25
    // Typical face width is around 0.4-0.6
    const eyeScale = eyeDistance * 3.5  // Adjusted multiplier
    const faceScale = faceWidth * 1.8   // Face width based scaling

    // Use weighted average for final scale
    const baseScale = (eyeScale * 0.7) + (faceScale * 0.3)

    return Math.max(0.5, Math.min(2.0, baseScale)) // Clamp between 0.5 and 2.0
  }

  // Toggle mirror compensation for camera mirroring
  setMirrorCompensation(enabled: boolean) {
    this.mirrorCompensation = enabled
    console.log(`🪞 Mirror compensation ${enabled ? 'enabled' : 'disabled'}`)
  }

  // Get current mirror compensation state
  getMirrorCompensation(): boolean {
    return this.mirrorCompensation
  }

  dispose() {
    if (this.faceLandmarker) {
      this.faceLandmarker.close()
      this.faceLandmarker = null
    }
    this.isInitialized = false
  }
}