# Static Glasses Troubleshooting

## Problem
Setelah implementasi positioning refinements, 3D object (kacamata) menjadi diam dan tidak mengikuti gerakan wajah.

## Root Cause Analysis

### Issue Identified
1. **Render loop tidak dimulai** - `startRenderLoop()` tidak dipanggil
2. **Smoothing tidak berjalan** - Target position di-set tapi tidak di-apply
3. **Face rotation tidak ter-apply** - Rotation calculation OK tapi tidak sampai ke model

## Fixes Applied

### 1. **Start Render Loop**
```typescript
// Di try-on-canvas.tsx initialization
glassesRendererRef.current.startRenderLoop()
console.log('🎬 Render loop started')
```

### 2. **Enhanced Fallback Mechanism**
```typescript
// Apply position immediately if render loop not running
if (!this.animationId || !this._hasInitSmooth) {
  console.log('⚡ Applying position immediately (fallback)')
  this.glassesModel.position.set(world.x, world.y, world.z)
  this.glassesModel.quaternion.setFromEuler(new THREE.Euler(rotation.x, rotation.y, rotation.z))
  this.glassesModel.scale.set(scale, scale, scale)
}
```

### 3. **Enhanced Debug Logging**
```typescript
// Render loop debug
console.log('🔄 Smoothing applied in render loop:', {
  hasModel: !!this.glassesModel,
  hasInitSmooth: this._hasInitSmooth,
  target: { x: this._targetPos.x.toFixed(2), y: this._targetPos.y.toFixed(2) },
  renderLoopActive: !!this.animationId
})

// Face rotation debug
console.log('🔄 Face rotation calculated:', {
  raw: { pitch: pitch.toFixed(3), yaw: yaw.toFixed(3), roll: eyeAngle.toFixed(3) },
  final: { x: result.x.toFixed(3), y: result.y.toFixed(3), z: result.z.toFixed(3) }
})
```

## Diagnostic Commands

### Check System Status
```javascript
// Check if render loop is running
window.debugGlasses.checkRenderLoop()

// Expected output:
{
  animationId: true,     // Render loop active
  hasModel: true,        // Model loaded
  hasInitSmooth: true    // Smoothing initialized
}
```

### Test Manual Movement
```javascript
// Test if positioning system works
window.debugGlasses.testMovement()

// Should see glasses move and rotate
```

### Monitor Console Logs
Look for these key logs:

#### Render Loop Active
```
🎬 Render loop started
🔄 Smoothing applied in render loop: { hasModel: true, hasInitSmooth: true, ... }
```

#### Face Detection Working
```
🔄 Face rotation calculated: { raw: {...}, final: {...} }
🎯 Blue dots positioning: { anchor: "center", world: {...} }
```

#### Positioning Applied
```
⚡ Applying position immediately (fallback)
```

## Expected Behavior

### When Working Correctly
1. **Console shows**:
   - `🎬 Render loop started`
   - `🔄 Smoothing applied in render loop` (periodic)
   - `🔄 Face rotation calculated` (periodic)
   - `🎯 Blue dots positioning` (when face detected)

2. **Visual behavior**:
   - Glasses follow face position
   - Rotation follows head turns (yaw/pitch/roll)
   - Smooth movement without jitter

### When Not Working
1. **Missing logs**:
   - No `🎬 Render loop started` → render loop not started
   - No `🔄 Smoothing applied` → smoothing not running
   - No `🔄 Face rotation` → face detection not working

2. **Visual behavior**:
   - Glasses static/frozen
   - No response to head movement
   - Position stuck at last known location

## Troubleshooting Steps

### Step 1: Check Render Loop
```javascript
window.debugGlasses.checkRenderLoop()
```

**If `animationId: false`**:
- Render loop not started
- Check initialization in try-on-canvas.tsx
- Look for `🎬 Render loop started` log

### Step 2: Check Model Loading
```javascript
console.log('Model loaded:', !!glassesRendererRef.current?.glassesModel)
```

**If `false`**:
- Model not loaded
- Check model URL and loading process
- Look for loading errors

### Step 3: Check Face Detection
Look for face detection logs:
```
🔄 Face rotation calculated: ...
🎯 Blue dots positioning: ...
```

**If missing**:
- Face detection not working
- Check camera permissions
- Check MediaPipe initialization

### Step 4: Test Manual Movement
```javascript
window.debugGlasses.testMovement()
```

**If no movement**:
- Positioning system broken
- Check console for errors
- Verify fallback mechanism

## Common Issues & Solutions

### Issue: Render Loop Not Starting
**Symptoms**: No smoothing logs, static glasses
**Solution**: 
```typescript
// Ensure startRenderLoop() is called after model loading
await glassesRendererRef.current.loadGlassesModel(modelUrl)
glassesRendererRef.current.startRenderLoop()
```

### Issue: Face Detection Not Working
**Symptoms**: No face rotation logs, no positioning updates
**Solution**:
- Check camera permissions
- Verify MediaPipe initialization
- Check face detection method calls

### Issue: Smoothing Not Applied
**Symptoms**: Target position set but model doesn't move
**Solution**:
- Check render loop is running
- Verify fallback mechanism
- Check model existence

### Issue: Wrong Rotation Direction
**Symptoms**: Head turns left, glasses turn right
**Solution**:
```javascript
// Toggle mirror compensation
window.debugGlasses.toggleMirrorCompensation()
window.debugGlasses.toggleMirrorX()
```

## Debug Commands Summary

```javascript
// System status
window.debugGlasses.checkRenderLoop()

// Test movement
window.debugGlasses.testMovement()

// Toggle mirroring
window.debugGlasses.toggleMirrorCompensation() // For rotation
window.debugGlasses.toggleMirrorX()            // For position

// Debug logging
window.debugGlasses.enableDebug()
window.debugGlasses.disableDebug()

// Test positioning
window.debugGlasses.testBlueDots('center')
window.debugGlasses.testBlueDots('left')
window.debugGlasses.testBlueDots('right')
```

## Expected Console Output (Working System)

```
🎬 Render loop started
🔄 Face rotation calculated: {
  raw: { pitch: "0.123", yaw: "-0.456", roll: "0.078" },
  final: { x: "0.123", y: "0.456", z: "-0.078" }
}
🎯 Blue dots positioning: {
  anchor: "center",
  normalized: { x: 0.5, y: 0.4 },
  mirrored: { x: 0.5, y: 0.4 },
  world: { x: "0.12", y: "-0.34", z: "0.00" }
}
🔄 Smoothing applied in render loop: {
  hasModel: true,
  hasInitSmooth: true,
  target: { x: "0.12", y: "-0.34", z: "0.00" },
  smoothed: { x: "0.11", y: "-0.33", z: "0.00" }
}
```

## Performance Notes

- Debug logging is limited to 1-5% of frames to avoid spam
- Fallback mechanism ensures immediate response during development
- Render loop provides smooth 60fps updates
- Face detection runs at camera frame rate (usually 30fps)

## Next Steps

1. **Run diagnostic commands** to identify specific issue
2. **Check console logs** for missing components
3. **Test manual movement** to verify positioning system
4. **Adjust mirror settings** if rotation direction wrong
5. **Monitor performance** and adjust debug logging as needed
