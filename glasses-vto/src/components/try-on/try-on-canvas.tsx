'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Camera, CameraOff, Download, Loader2, RotateCcw, Sparkles } from 'lucide-react'
import { FaceDetection } from '@/lib/mediapipe/face-detection'
import { GlassesRenderer } from '@/lib/three/glasses-renderer'
import { useTryOnStore } from '@/lib/store'
import { toast } from 'sonner'

interface TryOnCanvasProps {
  modelUrl?: string
  productId: string
  variantId: string
  onSnapshot?: (imageData: string) => void
}

export function TryOnCanvas({ 
  modelUrl = '/models/default-glasses.glb',
  productId, 
  variantId,
  onSnapshot 
}: TryOnCanvasProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null)
  const faceDetectorRef = useRef<FaceDetection | null>(null)
  const glassesRendererRef = useRef<GlassesRenderer | null>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isCameraOn, setIsCameraOn] = useState(false)
  const [showPermissionDialog, setShowPermissionDialog] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isDetecting, setIsDetecting] = useState(false)
  
  const { tryOnSettings, setTryOnSettings } = useTryOnStore()
  const animationFrameRef = useRef<number | null>(null)

  // Initialize camera
  const startCamera = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
        setIsCameraOn(true)
      }
    } catch (err: any) {
      console.error('Camera error:', err)
      if (err.name === 'NotAllowedError') {
        setShowPermissionDialog(true)
      } else {
        setError('Failed to access camera. Please check your device settings.')
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Stop camera
  const stopCamera = useCallback(() => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      videoRef.current.srcObject = null
      setIsCameraOn(false)
    }
  }, [])

  // Initialize Face Detection and Three.js
  useEffect(() => {
    const initializeComponents = async () => {
      try {
        setIsLoading(true)

        // Initialize face detector
        if (!faceDetectorRef.current) {
          faceDetectorRef.current = new FaceDetection()
          await faceDetectorRef.current.initialize()
        }

        // Initialize Three.js renderer
        if (overlayCanvasRef.current && !glassesRendererRef.current) {
          glassesRendererRef.current = new GlassesRenderer(
            overlayCanvasRef.current,
            640,
            480
          )
          
          // Load glasses model
          if (modelUrl) {
            await glassesRendererRef.current.loadGlassesModel(modelUrl)
          }
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Initialization error:', err)
        setError('Failed to initialize AR components')
        setIsLoading(false)
      }
    }

    initializeComponents()

    return () => {
      // Cleanup
      if (faceDetectorRef.current) {
        faceDetectorRef.current.dispose()
        faceDetectorRef.current = null
      }
      if (glassesRendererRef.current) {
        glassesRendererRef.current.dispose()
        glassesRendererRef.current = null
      }
    }
  }, [modelUrl])

  // Main detection and rendering loop
  const detectAndRender = useCallback(() => {
    if (!videoRef.current || !faceDetectorRef.current || !glassesRendererRef.current) {
      return
    }

    if (videoRef.current.readyState === videoRef.current.HAVE_ENOUGH_DATA) {
      setIsDetecting(true)

      // Detect faces
      const results = faceDetectorRef.current.detectFaces(videoRef.current)

      if (results && results.faceLandmarks && results.faceLandmarks.length > 0) {
        const landmarks = results.faceLandmarks
        const keyPoints = faceDetectorRef.current.getFaceKeyPoints(landmarks)

        if (keyPoints) {
          const rotation = faceDetectorRef.current.calculateFaceRotation(keyPoints)
          const baseScale = faceDetectorRef.current.calculateGlassesScale(keyPoints)
          const finalScale = baseScale * (tryOnSettings.zoom || 1)

          // Use new blue dots positioning method for accurate tracking
          glassesRendererRef.current.updateFromFaceLandmarksWithBlueDots(
            keyPoints,
            rotation,
            finalScale,
            'center', // anchor to center blue dot
            {
              x: tryOnSettings.offsetX || 0,
              y: tryOnSettings.offsetY || 0,
              z: tryOnSettings.offsetZ || 0
            },
            true // use auto scale
          )

          // Apply brightness setting with better filtering
          if (overlayCanvasRef.current) {
            const brightness = tryOnSettings.brightness / 100
            overlayCanvasRef.current.style.filter = `brightness(${brightness}) contrast(1.1) saturate(1.05)`
          }
        }
      } else {
        // No face detected, hide glasses or show placeholder
        setIsDetecting(false)
      }

      // Render glasses
      glassesRendererRef.current.render()
    }

    // Continue loop
    animationFrameRef.current = requestAnimationFrame(detectAndRender)
  }, [tryOnSettings])

  // Start/stop detection loop when camera state changes
  useEffect(() => {
    if (isCameraOn && !isLoading) {
      detectAndRender()
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
      setIsDetecting(false)
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
    }
  }, [isCameraOn, isLoading, detectAndRender])

  // Expose debug functions globally for testing glasses orientation
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).debugGlasses = {
        testCenter: () => {
          if (glassesRendererRef.current) {
            glassesRendererRef.current.updateGlassesPosition({ x: 0, y: 0, z: -150 })
            glassesRendererRef.current.updateGlassesRotation({ x: 0, y: 0, z: 0 })
            glassesRendererRef.current.updateGlassesScale(1.5)
            console.log('🎯 Glasses positioned at center with scale 1.5')
          }
        },
        testOrientation: () => {
          if (glassesRendererRef.current) {
            glassesRendererRef.current.testGlassesOrientation()
          } else {
            console.log('❌ Glasses renderer not available')
          }
        },
        setOrientation: (x: number, y: number, z: number) => {
          if (glassesRendererRef.current) {
            // Convert degrees to radians if values seem to be in degrees
            const xRad = Math.abs(x) > 10 ? x * (Math.PI / 180) : x
            const yRad = Math.abs(y) > 10 ? y * (Math.PI / 180) : y
            const zRad = Math.abs(z) > 10 ? z * (Math.PI / 180) : z

            glassesRendererRef.current.setGlassesBaseOrientation({ x: xRad, y: yRad, z: zRad })
          } else {
            console.log('❌ Glasses renderer not available')
          }
        },
        resetToCamera: () => {
          if (glassesRendererRef.current) {
            glassesRendererRef.current.setGlassesBaseOrientation({ x: 0, y: 0, z: 0 })
            console.log('✅ Glasses orientation reset to face camera')
          }
        },
        faceEyes: () => {
          if (glassesRendererRef.current) {
            glassesRendererRef.current.setGlassesBaseOrientation({ x: 0, y: Math.PI, z: 0 })
            console.log('🔄 Glasses orientation set to face eyes (180° rotation)')
          }
        },
        testBlueDots: (anchor: 'center' | 'left' | 'right' = 'center') => {
          if (glassesRendererRef.current && faceDetectorRef.current) {
            // Create mock blue dots for testing
            const mockBlueDots = {
              center: { x: 0.5, y: 0.4 },
              left: { x: 0.3, y: 0.4 },
              right: { x: 0.7, y: 0.4 }
            }

            glassesRendererRef.current.updateFromBlueDots(
              mockBlueDots,
              anchor,
              { x: 0, y: 0, z: 0 },
              1.5,
              {},
              0
            )
            console.log(`🎯 Testing blue dots positioning with anchor: ${anchor}`)
          }
        },
        switchAnchor: (anchor: 'center' | 'left' | 'right') => {
          console.log(`🔄 Switching to ${anchor} anchor for next face detection`)
          // This would need to be implemented in the main detection loop
          // For now, just log the intention
        }
      }
    }
  }, [])

  // Handle snapshot with proper mirroring and scaling
  const takeSnapshot = useCallback(() => {
    if (!canvasRef.current || !videoRef.current || !overlayCanvasRef.current || !glassesRendererRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size to match video dimensions
    const videoWidth = videoRef.current.videoWidth
    const videoHeight = videoRef.current.videoHeight
    canvas.width = videoWidth
    canvas.height = videoHeight

    // Save context state
    ctx.save()

    // Apply mirroring to match the display
    ctx.scale(-1, 1)
    ctx.translate(-videoWidth, 0)

    // Draw video frame (mirrored)
    ctx.drawImage(videoRef.current, 0, 0, videoWidth, videoHeight)

    // Restore context for overlay
    ctx.restore()
    ctx.save()

    // Apply same mirroring for glasses overlay
    ctx.scale(-1, 1)
    ctx.translate(-videoWidth, 0)

    // Draw glasses overlay with proper scaling
    const overlayCanvas = overlayCanvasRef.current
    ctx.drawImage(overlayCanvas, 0, 0, videoWidth, videoHeight)

    // Restore context
    ctx.restore()

    // Get image data
    const imageData = canvas.toDataURL('image/png', 0.95) // High quality

    // Callback or download
    if (onSnapshot) {
      onSnapshot(imageData)
    } else {
      // Download image
      const link = document.createElement('a')
      link.href = imageData
      link.download = `glasses-tryon-${Date.now()}.png`
      link.click()
    }

    toast.success('Snapshot saved!')
  }, [onSnapshot])

  return (
    <Card className="relative overflow-hidden">
      <div className="relative aspect-video bg-black">
        {/* Video Feed */}
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover mirror"
          style={{ transform: 'scaleX(-1)' }} // Mirror for selfie view
          playsInline
          muted
        />
        
        {/* Three.js Overlay Canvas */}
        <canvas
          ref={overlayCanvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
          style={{ transform: 'scaleX(-1)' }} // Mirror overlay too
        />
        
        {/* Hidden canvas for snapshots */}
        <canvas ref={canvasRef} className="hidden" />
        
        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        )}
        
        {/* Error Message */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80">
            <div className="text-center text-white p-4">
              <p className="mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </div>
          </div>
        )}
        
        {/* Camera Off State */}
        {!isCameraOn && !isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <CameraOff className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">Camera is off</p>
              <Button onClick={startCamera}>
                <Camera className="mr-2 h-4 w-4" />
                Turn On Camera
              </Button>
            </div>
          </div>
        )}
        
        {/* Detection Indicator */}
        {isDetecting && (
          <div className="absolute top-4 left-4 flex items-center gap-2 bg-green-500/20 backdrop-blur px-3 py-1 rounded-full">
            <Sparkles className="h-4 w-4 text-green-400" />
            <span className="text-sm text-green-400">Face Detected</span>
          </div>
        )}
      </div>
      
      {/* Controls */}
      <div className="p-4 space-y-4 bg-white">
        <div className="flex gap-2">
          {!isCameraOn ? (
            <Button onClick={startCamera} className="flex-1">
              <Camera className="mr-2 h-4 w-4" />
              Start Camera
            </Button>
          ) : (
            <Button onClick={stopCamera} variant="outline" className="flex-1">
              <CameraOff className="mr-2 h-4 w-4" />
              Stop Camera
            </Button>
          )}
          
          <Button 
            onClick={takeSnapshot} 
            disabled={!isCameraOn || isLoading}
            className="flex-1"
          >
            <Download className="mr-2 h-4 w-4" />
            Take Snapshot
          </Button>
        </div>
        
        {/* Adjustment Controls */}
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Brightness: {tryOnSettings.brightness}%
            </label>
            <Slider
              value={[tryOnSettings.brightness]}
              onValueChange={([value]) => setTryOnSettings({ brightness: value })}
              min={50}
              max={150}
              step={10}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">
              Size: {Math.round(tryOnSettings.zoom * 100)}%
            </label>
            <Slider
              value={[tryOnSettings.zoom * 100]}
              onValueChange={([value]) => setTryOnSettings({ zoom: value / 100 })}
              min={50}
              max={150}
              step={5}
            />
          </div>

          {/* Position Fine-tuning */}
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-3">Position Adjustment</h4>

            <div className="space-y-2">
              <div>
                <label className="text-xs text-gray-600 mb-1 block">
                  Horizontal: {tryOnSettings.offsetX > 0 ? '+' : ''}{tryOnSettings.offsetX}
                </label>
                <Slider
                  value={[tryOnSettings.offsetX]}
                  onValueChange={([value]) => setTryOnSettings({ offsetX: value })}
                  min={-50}
                  max={50}
                  step={1}
                />
              </div>

              <div>
                <label className="text-xs text-gray-600 mb-1 block">
                  Vertical: {tryOnSettings.offsetY > 0 ? '+' : ''}{tryOnSettings.offsetY}
                </label>
                <Slider
                  value={[tryOnSettings.offsetY]}
                  onValueChange={([value]) => setTryOnSettings({ offsetY: value })}
                  min={-50}
                  max={50}
                  step={1}
                />
              </div>

              <div>
                <label className="text-xs text-gray-600 mb-1 block">
                  Depth: {tryOnSettings.offsetZ > 0 ? '+' : ''}{tryOnSettings.offsetZ}
                </label>
                <Slider
                  value={[tryOnSettings.offsetZ]}
                  onValueChange={([value]) => setTryOnSettings({ offsetZ: value })}
                  min={-20}
                  max={20}
                  step={1}
                />
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="w-full mt-2"
              onClick={() => setTryOnSettings({ offsetX: 0, offsetY: 0, offsetZ: 0 })}
            >
              Reset Position
            </Button>
          </div>
        </div>
      </div>
      
      {/* Permission Dialog */}
      <Dialog open={showPermissionDialog} onOpenChange={setShowPermissionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Camera Permission Required</DialogTitle>
            <DialogDescription>
              To use the virtual try-on feature, we need access to your camera. 
              Your camera feed is processed locally and never leaves your device.
              <br /><br />
              Please allow camera access when prompted by your browser.
            </DialogDescription>
          </DialogHeader>
          <div className="flex gap-2 justify-end mt-4">
            <Button variant="outline" onClick={() => setShowPermissionDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => {
              setShowPermissionDialog(false)
              startCamera()
            }}>
              Grant Permission
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}