import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

export class GlassesRenderer {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private glassesModel: THREE.Group | null = null
  private loader: GLTFLoader
  private animationId: number | null = null

  constructor(canvas: HTMLCanvasElement, width: number, height: number) {
    // Initialize Scene
    this.scene = new THREE.Scene()
    this.scene.background = null // Transparent background

    // Initialize Camera
    this.camera = new THREE.PerspectiveCamera(
      50, // Field of view
      width / height, // Aspect ratio
      0.1, // Near clipping plane
      1000 // Far clipping plane
    )
    this.camera.position.z = 5

    // Initialize Renderer
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      alpha: true,
      antialias: true,
      preserveDrawingBuffer: true // For snapshot functionality
    })
    this.renderer.setSize(width, height)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.outputColorSpace = THREE.SRGBColorSpace

    // Setup Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
    this.scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4)
    directionalLight.position.set(0, 1, 1)
    this.scene.add(directionalLight)

    // Initialize Loader
    this.loader = new GLTFLoader()
  }

  async loadGlassesModel(modelUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Remove existing model if any
      if (this.glassesModel) {
        this.scene.remove(this.glassesModel)
        this.glassesModel = null
      }

      this.loader.load(
        modelUrl,
        (gltf) => {
          this.glassesModel = gltf.scene
          
          // Set initial properties
          this.glassesModel.traverse((child) => {
            if ((child as THREE.Mesh).isMesh) {
              const mesh = child as THREE.Mesh
              mesh.castShadow = true
              mesh.receiveShadow = true
              
              // Ensure materials are double-sided for better rendering
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(mat => {
                  mat.side = THREE.DoubleSide
                })
              } else {
                mesh.material.side = THREE.DoubleSide
              }
            }
          })

          this.scene.add(this.glassesModel)
          resolve()
        },
        (progress) => {
          console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%')
        },
        (error) => {
          console.error('Error loading model:', error)
          reject(error)
        }
      )
    })
  }

  updateGlassesPosition(position: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return
    
    this.glassesModel.position.set(position.x, position.y, position.z)
  }

  updateGlassesRotation(rotation: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return
    
    this.glassesModel.rotation.set(rotation.x, rotation.y, rotation.z)
  }

  updateGlassesScale(scale: number) {
    if (!this.glassesModel) return
    
    this.glassesModel.scale.set(scale, scale, scale)
  }

  // Update glasses transform from face landmarks
  updateFromFaceLandmarks(keyPoints: any, rotation: any, scale: number, canvasWidth: number = 640, canvasHeight: number = 480) {
    if (!this.glassesModel || !keyPoints) return

    // Calculate center position between eyes with better accuracy
    const centerX = (keyPoints.leftEyeCenter.x + keyPoints.rightEyeCenter.x) / 2
    const centerY = (keyPoints.leftEyeCenter.y + keyPoints.rightEyeCenter.y) / 2

    // Calculate vertical offset to position glasses properly on nose bridge
    const eyeToNoseOffset = keyPoints.noseBridge.y - centerY
    const adjustedY = centerY + (eyeToNoseOffset * 0.3) // Move slightly towards nose bridge

    // Convert normalized coordinates to Three.js coordinates
    // MediaPipe coordinates are normalized 0-1, need to map to Three.js world space
    const aspectRatio = canvasWidth / canvasHeight

    // Map to Three.js coordinate system with proper aspect ratio handling
    const x = (centerX - 0.5) * 8 * aspectRatio  // Horizontal positioning
    const y = -(adjustedY - 0.5) * 8             // Vertical positioning (inverted Y)
    const z = keyPoints.noseBridge.z ? keyPoints.noseBridge.z * 2 : 0 // Depth positioning

    this.updateGlassesPosition({ x, y, z })
    this.updateGlassesRotation(rotation)
    this.updateGlassesScale(scale)
  }

  // New method for more precise positioning with offset support
  updateFromFaceLandmarksAdvanced(
    keyPoints: any,
    rotation: any,
    scale: number,
    videoElement: HTMLVideoElement,
    offsets: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 }
  ) {
    if (!this.glassesModel || !keyPoints || !videoElement) return

    const videoWidth = videoElement.videoWidth
    const videoHeight = videoElement.videoHeight
    const aspectRatio = videoWidth / videoHeight

    // Calculate glasses center point (slightly above eye center, on nose bridge)
    const leftEye = keyPoints.leftEyeCenter
    const rightEye = keyPoints.rightEyeCenter
    const noseBridge = keyPoints.noseBridge

    // Weighted average for optimal glasses positioning
    const centerX = (leftEye.x + rightEye.x) / 2
    const centerY = (leftEye.y + rightEye.y) / 2

    // Adjust Y position to sit properly on nose bridge
    const eyeToNoseBridge = noseBridge.y - centerY
    const glassesY = centerY + (eyeToNoseBridge * 0.4) // 40% towards nose bridge

    // Convert to Three.js world coordinates
    const baseWorldX = (centerX - 0.5) * 6 * aspectRatio
    const baseWorldY = -(glassesY - 0.5) * 6
    const baseWorldZ = noseBridge.z ? noseBridge.z * 1.5 : 0

    // Apply user offsets (convert from UI range to world coordinates)
    const worldX = baseWorldX + (offsets.x / 100) * 2  // Scale offset to reasonable range
    const worldY = baseWorldY + (offsets.y / 100) * 2  // Scale offset to reasonable range
    const worldZ = baseWorldZ + (offsets.z / 100) * 0.5 // Smaller range for depth

    // Apply position
    this.updateGlassesPosition({ x: worldX, y: worldY, z: worldZ })
    this.updateGlassesRotation(rotation)
    this.updateGlassesScale(scale)
  }

  // Change glasses material/color
  updateGlassesMaterial(options: { 
    frameColor?: string; 
    lensOpacity?: number;
    lensColor?: string;
  }) {
    if (!this.glassesModel) return

    this.glassesModel.traverse((child) => {
      if ((child as THREE.Mesh).isMesh) {
        const mesh = child as THREE.Mesh
        
        // Update frame color
        if (options.frameColor && mesh.name.toLowerCase().includes('frame')) {
          if (Array.isArray(mesh.material)) {
            mesh.material.forEach(mat => {
              mat.color = new THREE.Color(options.frameColor)
            })
          } else {
            mesh.material.color = new THREE.Color(options.frameColor)
          }
        }
        
        // Update lens properties
        if (mesh.name.toLowerCase().includes('lens')) {
          if (Array.isArray(mesh.material)) {
            mesh.material.forEach(mat => {
              if (options.lensOpacity !== undefined) {
                mat.opacity = options.lensOpacity
                mat.transparent = true
              }
              if (options.lensColor) {
                mat.color = new THREE.Color(options.lensColor)
              }
            })
          } else {
            if (options.lensOpacity !== undefined) {
              mesh.material.opacity = options.lensOpacity
              mesh.material.transparent = true
            }
            if (options.lensColor) {
              mesh.material.color = new THREE.Color(options.lensColor)
            }
          }
        }
      }
    })
  }

  render() {
    this.renderer.render(this.scene, this.camera)
  }

  startRenderLoop(callback?: () => void) {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      
      if (callback) callback()
      
      this.render()
    }
    animate()
  }

  stopRenderLoop() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  resize(width: number, height: number) {
    this.camera.aspect = width / height
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(width, height)
  }

  getSnapshot(): string {
    this.render() // Ensure fresh render
    return this.renderer.domElement.toDataURL('image/png')
  }

  dispose() {
    this.stopRenderLoop()
    
    if (this.glassesModel) {
      this.scene.remove(this.glassesModel)
      this.glassesModel = null
    }
    
    this.renderer.dispose()
    this.scene.clear()
  }
}