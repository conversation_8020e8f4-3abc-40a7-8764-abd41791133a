# Troubleshooting: Static Glasses Issue

## Problem
After implementing the blue dots positioning system, the 3D glasses object is not following eye movements and appears static.

## Potential Causes & Solutions

### 1. **Smoothing Architecture Issue**
**Problem**: Smoothing moved to render loop but targets not being updated properly.

**Check**:
```javascript
// Open browser console and look for these logs:
// "🔄 Calling updateFromFaceLandmarksWithBlueDots with:"
// "🎯 updateFromFaceLandmarksWithBlueDots called:"
// "📍 Generated blue dots:"
// "🎯 Blue dots positioning:"
```

**Solution**: Ensure both target setting and fallback smoothing work:
- Target position/rotation set in `updateFromBlueDots()`
- Render loop applies smoothing every frame
- Fallback smoothing if render loop not running

### 2. **Method Not Being Called**
**Problem**: Old method still being used or new method not called.

**Check**:
```javascript
// Look for this log in console:
"🔄 Calling updateFromFaceLandmarksWithBlueDots with:"
```

**Solution**: Verify `try-on-canvas.tsx` uses correct method:
```typescript
glassesRendererRef.current.updateFromFaceLandmarksWithBlueDots(
  keyPoints,
  rotation,
  finalScale,
  'center',
  offsets,
  true
)
```

### 3. **Blue Dots Generation Failed**
**Problem**: `getBlueDotsFromLandmarks()` returns invalid data.

**Check**:
```javascript
// Look for this log:
"📍 Generated blue dots: {center: {...}, left: {...}, right: {...}}"
```

**Solution**: Verify keyPoints structure:
```javascript
// Required keyPoints structure:
{
  leftEyeCenter: { x: 0.0-1.0, y: 0.0-1.0, z: negative },
  rightEyeCenter: { x: 0.0-1.0, y: 0.0-1.0, z: negative },
  noseBridge: { x: 0.0-1.0, y: 0.0-1.0, z: negative }
}
```

### 4. **NDC to World Conversion Issue**
**Problem**: `ndcToWorld()` returns invalid coordinates.

**Check**:
```javascript
// Look for this log:
"🎯 Blue dots positioning: {world: {x: ..., y: ..., z: ...}}"
```

**Solution**: Verify world coordinates are reasonable:
- X: should change when head moves left/right
- Y: should change when head moves up/down
- Z: should be at target plane (usually 0)

### 5. **Smoothing Not Applied**
**Problem**: Target set but smoothing not working in render loop.

**Check**:
```javascript
// Enable debug and look for:
"🔄 Smoothing applied: {target: {...}, smoothed: {...}}"
```

**Solution**: Verify render loop is running and smoothing logic works:
- `this._hasInitSmooth` should be true after first call
- `this.animationId` should be set when render loop active
- Smoothed position should gradually approach target

### 6. **Model Not Loaded**
**Problem**: Glasses model not loaded when positioning called.

**Check**:
```javascript
// Look for this warning:
"❌ No glasses model loaded"
```

**Solution**: Ensure model loaded before positioning:
```javascript
// Check if model loaded:
console.log('Model loaded:', !!glassesRendererRef.current?.glassesModel)
```

## Debug Commands

### Enable Debug Mode
```javascript
window.debugGlasses.enableDebug()
```

### Test Static Positioning
```javascript
// Test if positioning works at all
window.debugGlasses.testBlueDots('center')
```

### Check Model State
```javascript
// Check if model exists and is positioned
console.log('Glasses model:', glassesRendererRef.current?.glassesModel?.position)
```

### Manual Position Test
```javascript
// Bypass blue dots and set position directly
window.debugGlasses.testCenter()
```

## Step-by-Step Diagnosis

### Step 1: Check Console Logs
1. Open browser console
2. Start camera and face detection
3. Look for positioning logs every frame

### Step 2: Verify Method Calls
```javascript
// Should see these logs:
"🔄 Calling updateFromFaceLandmarksWithBlueDots with:"
"🎯 updateFromFaceLandmarksWithBlueDots called:"
```

### Step 3: Check Blue Dots Generation
```javascript
// Should see:
"📍 Generated blue dots: {center: {x: 0.5, y: 0.4}, ...}"
```

### Step 4: Verify World Coordinates
```javascript
// Should see changing coordinates:
"🎯 Blue dots positioning: {world: {x: 1.2, y: -0.5, z: 0.0}}"
```

### Step 5: Check Smoothing
```javascript
// Should see gradual position changes:
"🔄 Smoothing applied: {target: {x: 1.2}, smoothed: {x: 1.1}}"
```

## Quick Fixes

### Fix 1: Force Immediate Update
```typescript
// In updateFromBlueDots, add immediate update:
if (!this._hasInitSmooth) {
  // ... existing init code ...
  // Apply immediately on first call
  this.glassesModel.position.copy(this._smoothedPos)
  this.glassesModel.quaternion.copy(this._smoothedQuat)
  this.glassesModel.scale.set(scale, scale, scale)
}
```

### Fix 2: Fallback Smoothing
```typescript
// Add fallback if render loop not running:
if (!this.animationId && this._hasInitSmooth) {
  const alpha = 0.35
  this._smoothedPos.lerp(this._targetPos, alpha)
  this._smoothedQuat.slerp(this._targetQuat, alpha)
  
  this.glassesModel.position.copy(this._smoothedPos)
  this.glassesModel.quaternion.copy(this._smoothedQuat)
  this.glassesModel.scale.set(scale, scale, scale)
}
```

### Fix 3: Disable Smoothing Temporarily
```typescript
// For testing, apply position directly:
this.glassesModel.position.set(world.x, world.y, world.z)
this.glassesModel.quaternion.setFromEuler(new THREE.Euler(rotation.x, rotation.y, rotation.z))
this.glassesModel.scale.set(scale, scale, scale)
```

## Expected Console Output

### Normal Operation
```
🔄 Calling updateFromFaceLandmarksWithBlueDots with: {hasKeyPoints: true, ...}
🎯 updateFromFaceLandmarksWithBlueDots called: {hasKeyPoints: true, hasModel: true, ...}
📍 Generated blue dots: {center: {x: 0.500, y: 0.366}, left: {x: 0.120, y: 0.366}, right: {x: 0.880, y: 0.366}}
📏 Final scale: 1.23
🎯 Blue dots positioning: {anchor: "center", normalized: {x: 0.500, y: 0.366}, world: {x: "0.00", y: "1.20", z: "0.00"}}
👁️ Face landmarks to blue dots: {eyeDistance: "0.422", autoScale: "1.23", anchor: "center", ...}
🔄 Smoothing applied: {target: {x: "0.00", y: "1.20", z: "0.00"}, smoothed: {x: "0.00", y: "1.15", z: "0.00"}}
```

### Problem Indicators
```
❌ No keyPoints provided
❌ No glasses model loaded
🎯 Blue dots positioning: {world: {x: "NaN", y: "NaN", z: "NaN"}}
// No smoothing logs = render loop not working
```

## Recovery Steps

1. **Temporary disable smoothing** - apply position directly
2. **Check model loading** - ensure glasses loaded before positioning
3. **Verify face detection** - ensure keyPoints are valid
4. **Test static positioning** - use debug functions
5. **Re-enable smoothing** - once basic positioning works
