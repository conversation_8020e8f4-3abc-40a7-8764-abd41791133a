'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { TryOnCanvas } from '@/components/try-on/try-on-canvas'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

export function TryOnDebugTest() {
  const [selectedModel, setSelectedModel] = useState('/models/default-glasses.gltf')
  const [debugEnabled, setDebugEnabled] = useState(true)
  const [modelStatus, setModelStatus] = useState('Not loaded')

  const testModels = [
    { name: 'Default Glasses', url: '/models/default-glasses.gltf', status: 'Available' },
    { name: 'Simple Glasses', url: '/models/simple-glasses.gltf', status: 'Available' },
    { name: 'Test Cube', url: '/models/simple-test-cube.gltf', status: 'Available' },
  ]

  // Test model loading
  const testModelLoad = async (modelUrl: string) => {
    try {
      setModelStatus('Testing...')
      const response = await fetch(modelUrl)
      if (response.ok) {
        setModelStatus(`✅ ${modelUrl} - Available`)
      } else {
        setModelStatus(`❌ ${modelUrl} - Not found (${response.status})`)
      }
    } catch (error) {
      setModelStatus(`❌ ${modelUrl} - Error: ${error.message}`)
    }
  }

  const debugCommands = [
    {
      name: 'Test Center Position',
      command: 'window.debugGlasses?.testCenter()',
      description: 'Position glasses at screen center'
    },
    {
      name: 'Test Red Cube',
      command: 'window.debugGlasses?.testCube()',
      description: 'Show red test cube for 3 seconds (visibility test)'
    },
    {
      name: 'Test Simple Glasses',
      command: 'window.debugGlasses?.testSimpleGlasses()',
      description: 'Render simple glasses shape (no GLTF loading)'
    },
    {
      name: 'Test Face Position',
      command: 'window.debugGlasses?.testFace(0.5, 0.4)',
      description: 'Position glasses at typical face location'
    },
    {
      name: 'Manual Position Test',
      command: 'window.debugGlasses?.testManual(320, 0, -100, 2)',
      description: 'Test manual positioning with appropriate scale'
    },
    {
      name: 'Get Camera Position',
      command: 'console.log(window.debugGlasses?.getCamera())',
      description: 'Log current camera position to console'
    },
    {
      name: 'Get Glasses Position',
      command: 'console.log(window.debugGlasses?.getGlasses())',
      description: 'Log current glasses position to console'
    },
    {
      name: 'Toggle Debug Mode',
      command: 'window.debugGlasses?.toggleDebug()',
      description: 'Toggle debug logging on/off'
    },
    {
      name: 'Toggle Debug Overlay',
      command: 'window.debugGlasses?.toggleOverlay()',
      description: 'Toggle debug overlay display'
    },
    {
      name: 'Get Debug Stats',
      command: 'console.log(window.debugGlasses?.getStats())',
      description: 'Log current debug statistics'
    },
    {
      name: 'Reset Stats',
      command: 'window.debugGlasses?.resetStats()',
      description: 'Reset debug statistics counters'
    }
  ]

  const executeCommand = (command: string) => {
    try {
      eval(command)
    } catch (error) {
      console.error('Debug command error:', error)
    }
  }

  return (
    <Card className="p-6">
      <h2 className="text-2xl font-bold mb-4">Try-On Debug Test</h2>
      
      <div className="space-y-6">
        {/* Model Selection */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Test Models</h3>
          <div className="flex gap-2 flex-wrap mb-3">
            {testModels.map((model) => (
              <Button
                key={model.url}
                onClick={() => setSelectedModel(model.url)}
                variant={selectedModel === model.url ? "default" : "outline"}
                size="sm"
              >
                {model.name}
                {selectedModel === model.url && <Badge className="ml-2">Active</Badge>}
              </Button>
            ))}
          </div>

          {/* Model Testing */}
          <div className="bg-gray-50 p-3 rounded mb-3">
            <div className="flex gap-2 items-center mb-2">
              <Button
                onClick={() => testModelLoad(selectedModel)}
                size="sm"
                variant="outline"
              >
                Test Model Load
              </Button>
              <span className="text-sm">{modelStatus}</span>
            </div>
          </div>

          <p className="text-sm text-gray-600 mt-2">
            Current model: <code className="bg-gray-100 px-1 rounded">{selectedModel}</code>
          </p>
        </div>

        <Separator />

        {/* Try-On Component */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Try-On Component</h3>
          <TryOnCanvas
            modelUrl={selectedModel}
            productId="debug-test"
            variantId="debug-variant"
          />
        </div>

        <Separator />

        {/* Debug Commands */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Debug Commands</h3>
          <p className="text-sm text-gray-600 mb-4">
            These commands interact with the debug methods exposed to <code>window.debugGlasses</code>
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {debugCommands.map((cmd, index) => (
              <div key={index} className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-sm">{cmd.name}</h4>
                  <Button
                    onClick={() => executeCommand(cmd.command)}
                    size="sm"
                    variant="outline"
                  >
                    Run
                  </Button>
                </div>
                <p className="text-xs text-gray-600 mb-2">{cmd.description}</p>
                <code className="text-xs bg-gray-100 p-1 rounded block">
                  {cmd.command}
                </code>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Instructions */}
        <div className="text-sm text-gray-600">
          <h3 className="font-semibold mb-2">Debug Instructions:</h3>
          <ol className="list-decimal list-inside space-y-1">
            <li>Start the camera using the "Start Camera" button</li>
            <li>Enable debug mode using the "Debug ON" button</li>
            <li>Enable debug overlay using the "Overlay" button to see real-time stats</li>
            <li>Use the debug commands above to test positioning and functionality</li>
            <li>Check browser console for detailed debug logs</li>
            <li>Try different models to test loading and rendering</li>
          </ol>
          
          <h3 className="font-semibold mt-4 mb-2">Expected Behavior:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li>Camera should start and show video feed</li>
            <li>Face detection should work and show "Face Detected" indicator</li>
            <li>Debug overlay should show FPS, face count, render count, etc.</li>
            <li>Glasses should appear and track face movement</li>
            <li>Debug commands should work without errors</li>
            <li>Console should show detailed debug information when debug mode is on</li>
          </ul>
        </div>
      </div>
    </Card>
  )
}
